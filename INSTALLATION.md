# دليل التثبيت والتشغيل السريع
## Quick Installation Guide

## المتطلبات الأساسية

### 1. XAMPP (الطريقة الأسهل)
- تحميل وتثبيت XAMPP من: https://www.apachefriends.org/
- تشغيل Apache و MySQL من لوحة تحكم XAMPP

### 2. أو استخدام خادم PHP المدمج
- PHP 7.4 أو أحدث
- MySQL أو MariaDB

## خطوات التثبيت

### الطريقة الأولى: باستخدام XAMPP

1. **نسخ الملفات**
   ```
   نسخ مجلد المشروع إلى: C:\xampp\htdocs\employee_management\
   ```

2. **إنشاء قاعدة البيانات**
   - افتح http://localhost/phpmyadmin
   - أنشئ قاعدة بيانات جديدة باسم: `employee_management`
   - اختر ترميز: `utf8mb4_unicode_ci`
   - استورد ملف `database.sql`

3. **تشغيل النظام**
   - افتح المتصفح
   - انتقل إلى: http://localhost/employee_management/

### الطريقة الثانية: خادم PHP المدمج

1. **إعداد قاعدة البيانات**
   ```bash
   mysql -u root -p
   CREATE DATABASE employee_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   USE employee_management;
   SOURCE database.sql;
   ```

2. **تشغيل الخادم**
   ```bash
   cd employee_management
   php -S localhost:8000
   ```

3. **فتح النظام**
   - افتح المتصفح
   - انتقل إلى: http://localhost:8000

## بيانات الدخول الافتراضية

```
اسم المستخدم: admin
كلمة المرور: password
```

## إعدادات قاعدة البيانات

إذا كانت إعدادات قاعدة البيانات مختلفة، عدل ملف `config/database.php`:

```php
define('DB_HOST', 'localhost');     // عنوان الخادم
define('DB_NAME', 'employee_management');  // اسم قاعدة البيانات
define('DB_USER', 'root');          // اسم المستخدم
define('DB_PASS', '');              // كلمة المرور
```

## اختبار النظام

### 1. تسجيل الدخول
- استخدم البيانات الافتراضية
- يجب أن تظهر لوحة التحكم

### 2. إضافة موظف تجريبي
- انقر على "إضافة موظف جديد"
- املأ البيانات المطلوبة
- احفظ البيانات

### 3. عرض الموظفين
- انقر على "عرض الموظفين"
- يجب أن تظهر قائمة الموظفين

## حل المشاكل الشائعة

### خطأ "لا يمكن الاتصال بقاعدة البيانات"
```
الحلول:
1. تأكد من تشغيل MySQL
2. تحقق من بيانات الاتصال في config/database.php
3. تأكد من وجود قاعدة البيانات
```

### خطأ "صفحة فارغة"
```
الحلول:
1. تحقق من ملفات سجل الأخطاء
2. تأكد من صحة مسار الملفات
3. تحقق من صلاحيات الملفات
```

### مشاكل الترميز العربي
```
الحلول:
1. تأكد من استخدام utf8mb4 في قاعدة البيانات
2. تحقق من إعدادات الخادم
3. تأكد من وجود meta charset في HTML
```

### خطأ في الصلاحيات
```
الحلول:
1. تأكد من تسجيل الدخول
2. تحقق من دور المستخدم
3. راجع جدول users في قاعدة البيانات
```

## الميزات المتاحة

### ✅ تم تطويرها
- نظام تسجيل الدخول والصلاحيات
- إضافة وعرض الموظفين
- البحث والتصفية الأساسية
- واجهة مستخدم متجاوبة
- التحقق من صحة البيانات
- سجل التدقيق الأساسي

### 🔄 قيد التطوير
- تعديل بيانات الموظفين
- التقارير المتقدمة
- النسخ الاحتياطي التلقائي
- إدارة المستخدمين
- تصدير البيانات

### 📋 مخطط للمستقبل
- ربط مع أنظمة الرواتب
- تطبيق الهاتف المحمول
- إشعارات البريد الإلكتروني
- لوحة تحكم متقدمة

## الدعم الفني

### للمساعدة:
1. راجع ملف README.md للتفاصيل الكاملة
2. تحقق من ملفات السجل للأخطاء
3. تأكد من تحديث PHP و MySQL

### معلومات النظام:
- **اللغة**: PHP 7.4+
- **قاعدة البيانات**: MySQL 5.7+ / MariaDB 10.2+
- **الواجهة**: Bootstrap 5 + Font Awesome
- **الترميز**: UTF-8 (دعم كامل للعربية)

---

**نصائح مهمة:**
- غير كلمة المرور الافتراضية فوراً
- قم بعمل نسخة احتياطية دورية
- تأكد من تحديث النظام بانتظام
- استخدم HTTPS في البيئة الإنتاجية
