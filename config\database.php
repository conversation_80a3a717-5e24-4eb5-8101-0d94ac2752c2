<?php
/**
 * إعدادات قاعدة البيانات
 * Database Configuration
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'employee_management');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// إعدادات النظام
define('SITE_NAME', 'نظام إدارة الموظفين');
define('SITE_URL', 'http://localhost/employee_management/');
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $charset = DB_CHARSET;
    public $conn;

    /**
     * الاتصال بقاعدة البيانات
     */
    public function getConnection() {
        $this->conn = null;
        
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ];
            
            $this->conn = new PDO($dsn, $this->username, $this->password, $options);
            
        } catch(PDOException $exception) {
            echo "خطأ في الاتصال: " . $exception->getMessage();
            die();
        }
        
        return $this->conn;
    }

    /**
     * تنفيذ استعلام مع معاملات
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch(PDOException $e) {
            throw new Exception("خطأ في تنفيذ الاستعلام: " . $e->getMessage());
        }
    }

    /**
     * الحصول على سجل واحد
     */
    public function fetch($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }

    /**
     * الحصول على جميع السجلات
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }

    /**
     * إدراج سجل جديد
     */
    public function insert($table, $data) {
        $fields = array_keys($data);
        $placeholders = ':' . implode(', :', $fields);
        $fields_str = implode(', ', $fields);
        
        $sql = "INSERT INTO {$table} ({$fields_str}) VALUES ({$placeholders})";
        
        $stmt = $this->query($sql, $data);
        return $this->conn->lastInsertId();
    }

    /**
     * تحديث سجل
     */
    public function update($table, $data, $where, $where_params = []) {
        $set_clause = [];
        foreach ($data as $field => $value) {
            $set_clause[] = "{$field} = :{$field}";
        }
        $set_str = implode(', ', $set_clause);
        
        $sql = "UPDATE {$table} SET {$set_str} WHERE {$where}";
        
        $params = array_merge($data, $where_params);
        return $this->query($sql, $params);
    }

    /**
     * حذف سجل
     */
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        return $this->query($sql, $params);
    }

    /**
     * عد السجلات
     */
    public function count($table, $where = '1=1', $params = []) {
        $sql = "SELECT COUNT(*) as count FROM {$table} WHERE {$where}";
        $result = $this->fetch($sql, $params);
        return $result['count'];
    }
}

// إنشاء اتصال عام
$database = new Database();
$db = $database->getConnection();

// دالة للحصول على اتصال قاعدة البيانات
function getDB() {
    global $db;
    return $db;
}

// دالة للحصول على كائن قاعدة البيانات
function getDatabase() {
    global $database;
    return $database;
}
?>
