<?php
/**
 * ملف الإعدادات الرئيسي
 * Main configuration file
 */

// منع الوصول المباشر
if (!defined('INCLUDED')) {
    define('INCLUDED', true);
}

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'employee_management');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// إعدادات الجلسة
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // تغيير إلى 1 في HTTPS
session_start();

// إعدادات الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../logs/error.log');

// إعدادات التوقيت
date_default_timezone_set('Africa/Cairo');

// إعدادات الرفع
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB
define('UPLOAD_PATH', __DIR__ . '/../uploads/');
define('EMPLOYEE_PHOTOS_PATH', UPLOAD_PATH . 'employee_photos/');
define('NATIONAL_ID_PHOTOS_PATH', UPLOAD_PATH . 'national_id_photos/');

// إنشاء المجلدات إذا لم تكن موجودة
$directories = [
    __DIR__ . '/../logs',
    UPLOAD_PATH,
    EMPLOYEE_PHOTOS_PATH,
    NATIONAL_ID_PHOTOS_PATH
];

foreach ($directories as $dir) {
    if (!file_exists($dir)) {
        mkdir($dir, 0755, true);
    }
}

// إنشاء ملفات .htaccess للحماية
$htaccess_content = "# حماية المجلد
Options -Indexes
<Files *.php>
    Deny from all
</Files>

# السماح بأنواع الصور فقط
<FilesMatch \"\.(jpg|jpeg|png|gif|webp|pdf)$\">
    Allow from all
</FilesMatch>";

$htaccess_files = [
    EMPLOYEE_PHOTOS_PATH . '.htaccess',
    NATIONAL_ID_PHOTOS_PATH . '.htaccess'
];

foreach ($htaccess_files as $file) {
    if (!file_exists($file)) {
        file_put_contents($file, $htaccess_content);
    }
}

// كلاس قاعدة البيانات
class Database {
    private $pdo;
    
    public function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $this->pdo = new PDO($dsn, DB_USER, DB_PASS, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ]);
        } catch (PDOException $e) {
            die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
        }
    }
    
    public function fetchAll($sql, $params = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Database Error: " . $e->getMessage());
            return [];
        }
    }
    
    public function fetchOne($sql, $params = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Database Error: " . $e->getMessage());
            return false;
        }
    }
    
    public function fetchColumn($sql, $params = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchColumn();
        } catch (PDOException $e) {
            error_log("Database Error: " . $e->getMessage());
            return 0;
        }
    }
    
    public function execute($sql, $params = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute($params);
        } catch (PDOException $e) {
            error_log("Database Error: " . $e->getMessage());
            return false;
        }
    }
    
    public function insert($table, $data) {
        try {
            $columns = implode(',', array_keys($data));
            $placeholders = ':' . implode(', :', array_keys($data));
            $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($data);
            return $this->pdo->lastInsertId();
        } catch (PDOException $e) {
            error_log("Database Error: " . $e->getMessage());
            return false;
        }
    }
    
    public function update($table, $data, $where, $whereParams = []) {
        try {
            $setClause = [];
            foreach (array_keys($data) as $key) {
                $setClause[] = "{$key} = :{$key}";
            }
            $setClause = implode(', ', $setClause);
            
            $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
            
            $params = array_merge($data, $whereParams);
            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute($params);
        } catch (PDOException $e) {
            error_log("Database Error: " . $e->getMessage());
            return false;
        }
    }
    
    public function delete($table, $where, $params = []) {
        try {
            $sql = "DELETE FROM {$table} WHERE {$where}";
            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute($params);
        } catch (PDOException $e) {
            error_log("Database Error: " . $e->getMessage());
            return false;
        }
    }
    
    public function count($table, $where = '', $params = []) {
        try {
            $sql = "SELECT COUNT(*) FROM {$table}";
            if ($where) {
                $sql .= " WHERE {$where}";
            }
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchColumn();
        } catch (PDOException $e) {
            error_log("Database Error: " . $e->getMessage());
            return 0;
        }
    }
    
    public function beginTransaction() {
        return $this->pdo->beginTransaction();
    }
    
    public function commit() {
        return $this->pdo->commit();
    }
    
    public function rollback() {
        return $this->pdo->rollback();
    }
}

// إنشاء متغير قاعدة البيانات العام
$db = new Database();

// دالة للحصول على قاعدة البيانات
function getDatabase() {
    global $db;
    return $db;
}

// إعدادات التطبيق
define('APP_NAME', 'نظام إدارة الموظفين');
define('APP_VERSION', '2.0');
define('APP_URL', 'http://localhost/h/');

// أدوار المستخدمين
define('ROLE_ADMIN', 'admin');
define('ROLE_HR_MANAGER', 'hr_manager');
define('ROLE_HR_EMPLOYEE', 'hr_employee');
define('ROLE_VIEWER', 'viewer');

// حالات الموظفين
define('EMPLOYEE_ACTIVE', 'active');
define('EMPLOYEE_INACTIVE', 'inactive');
define('EMPLOYEE_TERMINATED', 'terminated');

// أنواع الملفات المسموحة
define('ALLOWED_IMAGE_TYPES', ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']);
define('ALLOWED_DOCUMENT_TYPES', ['application/pdf']);
define('ALLOWED_FILE_TYPES', array_merge(ALLOWED_IMAGE_TYPES, ALLOWED_DOCUMENT_TYPES));

// رسائل النظام
define('MSG_SUCCESS', 'success');
define('MSG_ERROR', 'error');
define('MSG_WARNING', 'warning');
define('MSG_INFO', 'info');

// إعدادات الصفحات
define('RECORDS_PER_PAGE', 20);
define('MAX_SEARCH_RESULTS', 100);

// إعدادات الأمان
define('SESSION_TIMEOUT', 3600); // ساعة واحدة
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 دقيقة

// تحديد المسارات
define('ROOT_PATH', dirname(__DIR__));
define('INCLUDES_PATH', ROOT_PATH . '/includes');
define('PAGES_PATH', ROOT_PATH . '/pages');

// دالة لتسجيل الأخطاء
function logError($message, $file = '', $line = '') {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] ";
    
    if ($file && $line) {
        $logMessage .= "File: {$file}, Line: {$line} - ";
    }
    
    $logMessage .= $message . PHP_EOL;
    
    error_log($logMessage, 3, __DIR__ . '/../logs/error.log');
}

// دالة لتسجيل العمليات
function logActivity($action, $details = '', $user_id = null) {
    global $db;
    
    if (!$user_id && isset($_SESSION['user_id'])) {
        $user_id = $_SESSION['user_id'];
    }
    
    try {
        $db->insert('activity_logs', [
            'user_id' => $user_id,
            'action' => $action,
            'details' => $details,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'created_at' => date('Y-m-d H:i:s')
        ]);
    } catch (Exception $e) {
        logError("Failed to log activity: " . $e->getMessage());
    }
}

// دالة للتحقق من وجود الجداول وإنشاؤها إذا لزم الأمر
function ensureTablesExist() {
    global $db;
    
    // التحقق من وجود جدول activity_logs
    $tableExists = $db->fetchColumn("
        SELECT COUNT(*) 
        FROM information_schema.tables 
        WHERE table_schema = '" . DB_NAME . "' 
        AND table_name = 'activity_logs'
    ");
    
    if (!$tableExists) {
        $sql = "
        CREATE TABLE activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NULL,
            action VARCHAR(255) NOT NULL,
            details TEXT NULL,
            ip_address VARCHAR(45) NULL,
            user_agent TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        
        $db->execute($sql);
    }
}

// تشغيل التحقق من الجداول
ensureTablesExist();
?>
