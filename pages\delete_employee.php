<?php
/**
 * حذف موظف
 * Delete Employee
 */

require_once '../includes/functions.php';

// التحقق من الصلاحيات
requirePermission('hr_manager');

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة طلب غير مسموحة']);
    exit();
}

// قراءة البيانات
$input = json_decode(file_get_contents('php://input'), true);
$employee_id = isset($input['id']) ? (int)$input['id'] : 0;

if (empty($employee_id)) {
    echo json_encode(['success' => false, 'message' => 'معرف الموظف مطلوب']);
    exit();
}

$db = getDatabase();

try {
    // التحقق من وجود الموظف
    $employee = $db->fetch("SELECT * FROM employees WHERE id = ?", [$employee_id]);
    
    if (!$employee) {
        echo json_encode(['success' => false, 'message' => 'الموظف غير موجود']);
        exit();
    }
    
    // تسجيل في سجل التدقيق قبل الحذف
    logAudit($employee_id, 'DELETE');
    
    // حذف الموظف
    $db->delete('employees', 'id = ?', [$employee_id]);
    
    echo json_encode([
        'success' => true, 
        'message' => 'تم حذف الموظف بنجاح: ' . $employee['first_name'] . ' ' . $employee['family_name']
    ]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء الحذف: ' . $e->getMessage()]);
}
?>
