/**
 * نظام إدارة الموظفين - ملف التنسيقات المخصص
 * Employee Management System - Custom Styles
 */

/* الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

/* المتغيرات */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #11998e;
    --warning-color: #f093fb;
    --danger-color: #fc466b;
    --info-color: #3f5efb;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 10px;
    --box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    --transition: all 0.3s ease;
}

/* الإعدادات العامة */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background-color: var(--light-color);
    line-height: 1.6;
    color: var(--dark-color);
}

/* التدرجات اللونية */
.gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.gradient-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #38ef7d 100%);
}

.gradient-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, var(--info-color) 100%);
}

.gradient-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, var(--info-color) 100%);
}

/* الشريط العلوي */
.navbar {
    background: var(--gradient-primary) !important;
    box-shadow: var(--box-shadow);
    border-bottom: none;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: white !important;
}

.navbar-nav .nav-link {
    color: rgba(255,255,255,0.9) !important;
    font-weight: 500;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    color: white !important;
    transform: translateY(-1px);
}

/* الشريط الجانبي */
.sidebar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    min-height: calc(100vh - 56px);
    color: white;
    box-shadow: var(--box-shadow);
}

.sidebar .nav-link {
    color: rgba(255,255,255,0.8);
    padding: 12px 20px;
    border-radius: var(--border-radius);
    margin: 2px 0;
    transition: var(--transition);
    font-weight: 500;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    color: white;
    background-color: rgba(255,255,255,0.1);
    transform: translateX(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.sidebar .nav-link i {
    margin-left: 10px;
    width: 20px;
    text-align: center;
}

/* البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: 15px 15px 0 0 !important;
    padding: 15px 20px;
    border-bottom: none;
}

.card-title {
    margin-bottom: 0;
    font-weight: 600;
}

.card-body {
    padding: 20px;
}

/* الأزرار */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    border: none;
    padding: 10px 20px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #38ef7d 100%);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #f093fb 100%);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #3f5efb 100%);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
}

/* النماذج */
.form-control,
.form-select {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    transition: var(--transition);
    padding: 12px 15px;
    font-size: 14px;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* الجداول */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    font-weight: 600;
    padding: 15px;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

.table td {
    padding: 12px 15px;
    vertical-align: middle;
    border-color: #f1f3f4;
}

/* الشارات */
.badge {
    border-radius: 20px;
    padding: 8px 12px;
    font-weight: 500;
    font-size: 0.75rem;
}

/* بطاقات الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.2);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 5px;
}

.stats-label {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 500;
}

/* التنبيهات */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 15px 20px;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #38ef7d 100%);
    color: white;
}

.alert-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #3f5efb 100%);
    color: white;
}

.alert-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #f093fb 100%);
    color: white;
}

.alert-info {
    background: linear-gradient(135deg, var(--info-color) 0%, var(--primary-color) 100%);
    color: white;
}

/* الترقيم */
.pagination .page-link {
    border-radius: var(--border-radius);
    margin: 0 2px;
    border: 2px solid #e9ecef;
    color: var(--primary-color);
    transition: var(--transition);
}

.pagination .page-item.active .page-link {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.pagination .page-link:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* التحميل */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 56px;
        right: -100%;
        width: 250px;
        height: calc(100vh - 56px);
        z-index: 1000;
        transition: var(--transition);
    }
    
    .sidebar.show {
        right: 0;
    }
    
    .main-content {
        margin-right: 0 !important;
    }
    
    .stats-card {
        margin-bottom: 15px;
    }
    
    .stats-number {
        font-size: 2rem;
    }
    
    .table-responsive {
        border-radius: var(--border-radius);
    }
}

/* تحسينات إضافية */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.border-gradient {
    border: 2px solid;
    border-image: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) 1;
}

/* تأثيرات الحركة */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

/* تحسينات الطباعة */
@media print {
    .sidebar,
    .btn-toolbar,
    .pagination,
    .card-header {
        display: none !important;
    }
    
    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .table {
        font-size: 12px;
    }
}
