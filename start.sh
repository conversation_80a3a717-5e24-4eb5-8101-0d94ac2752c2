#!/bin/bash

echo "========================================"
echo "    نظام إدارة الموظفين"
echo "    Employee Management System"
echo "========================================"
echo

echo "جاري بدء تشغيل الخادم..."
echo "Starting server..."
echo

echo "الخادم يعمل على: http://localhost:8000"
echo "Server running on: http://localhost:8000"
echo

echo "للإيقاف اضغط Ctrl+C"
echo "To stop press Ctrl+C"
echo

echo "========================================"
echo "بيانات الدخول الافتراضية:"
echo "Default login credentials:"
echo "اسم المستخدم / Username: admin"
echo "كلمة المرور / Password: password"
echo "========================================"
echo

# التحقق من وجود PHP
if ! command -v php &> /dev/null; then
    echo "خطأ: PHP غير مثبت"
    echo "Error: PHP is not installed"
    exit 1
fi

# التحقق من إصدار PHP
PHP_VERSION=$(php -v | head -n 1 | cut -d " " -f 2 | cut -d "." -f 1,2)
echo "إصدار PHP: $PHP_VERSION"
echo "PHP Version: $PHP_VERSION"
echo

# بدء الخادم
php -S localhost:8000
