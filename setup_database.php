<?php
/**
 * إعداد قاعدة البيانات التلقائي
 * Automatic Database Setup
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>إعداد قاعدة البيانات - Database Setup</h1>";

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database_name = 'employee_management';

try {
    // Step 1: Connect to MySQL server
    echo "<h2>الخطوة 1: الاتصال بخادم MySQL</h2>";
    $pdo = new PDO("mysql:host=$host", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    echo "✅ تم الاتصال بخادم MySQL بنجاح<br>";

    // Step 2: Check if database exists
    echo "<h2>الخطوة 2: فحص وجود قاعدة البيانات</h2>";
    $stmt = $pdo->query("SHOW DATABASES LIKE '$database_name'");
    $database_exists = $stmt->rowCount() > 0;

    if ($database_exists) {
        echo "⚠️ قاعدة البيانات '$database_name' موجودة بالفعل<br>";
    } else {
        echo "ℹ️ قاعدة البيانات '$database_name' غير موجودة - سيتم إنشاؤها<br>";
    }

    // Step 3: Create database if it doesn't exist
    echo "<h2>الخطوة 3: إنشاء قاعدة البيانات</h2>";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS $database_name CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ تم إنشاء قاعدة البيانات '$database_name' بنجاح<br>";

    // Step 4: Connect to the new database
    echo "<h2>الخطوة 4: الاتصال بقاعدة البيانات</h2>";
    $pdo = new PDO("mysql:host=$host;dbname=$database_name;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    echo "✅ تم الاتصال بقاعدة البيانات '$database_name' بنجاح<br>";

    // Step 5: Check if tables exist
    echo "<h2>الخطوة 5: فحص الجداول</h2>";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll();

    if (count($tables) > 0) {
        echo "ℹ️ الجداول الموجودة:<br>";
        foreach ($tables as $table) {
            echo "- " . $table["Tables_in_$database_name"] . "<br>";
        }
    } else {
        echo "ℹ️ لا توجد جداول - سيتم إنشاؤها من ملف database.sql<br>";
        
        // Step 6: Read and execute SQL file
        echo "<h2>الخطوة 6: تنفيذ ملف SQL</h2>";
        
        if (file_exists('database.sql')) {
            $sql_content = file_get_contents('database.sql');
            
            // Remove the CREATE DATABASE and USE statements since we already created the database
            $sql_content = preg_replace('/CREATE DATABASE.*?;/i', '', $sql_content);
            $sql_content = preg_replace('/USE.*?;/i', '', $sql_content);
            
            // Split SQL into individual statements
            $statements = array_filter(array_map('trim', explode(';', $sql_content)));
            
            $success_count = 0;
            $error_count = 0;
            
            foreach ($statements as $statement) {
                if (!empty($statement)) {
                    try {
                        $pdo->exec($statement);
                        $success_count++;
                    } catch (PDOException $e) {
                        echo "⚠️ خطأ في تنفيذ الاستعلام: " . $e->getMessage() . "<br>";
                        $error_count++;
                    }
                }
            }
            
            echo "✅ تم تنفيذ $success_count استعلام بنجاح<br>";
            if ($error_count > 0) {
                echo "⚠️ فشل في تنفيذ $error_count استعلام<br>";
            }
            
        } else {
            echo "❌ ملف database.sql غير موجود<br>";
        }
    }

    // Step 7: Verify setup
    echo "<h2>الخطوة 7: التحقق من الإعداد</h2>";
    
    // Check tables again
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll();
    echo "عدد الجداول: " . count($tables) . "<br>";
    
    // Check if admin user exists
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
        $result = $stmt->fetch();
        echo "عدد المديرين: " . $result['count'] . "<br>";
        
        if ($result['count'] > 0) {
            echo "✅ تم إنشاء المستخدم الافتراضي<br>";
            echo "<strong>بيانات تسجيل الدخول:</strong><br>";
            echo "اسم المستخدم: admin<br>";
            echo "كلمة المرور: password<br>";
        }
    } catch (Exception $e) {
        echo "⚠️ خطأ في فحص المستخدمين: " . $e->getMessage() . "<br>";
    }

    echo "<h2>✅ تم إعداد قاعدة البيانات بنجاح!</h2>";
    echo "<p><a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الذهاب للنظام</a></p>";
    echo "<p><small>يمكنك حذف هذا الملف (setup_database.php) بعد الانتهاء من الإعداد</small></p>";

} catch (PDOException $e) {
    echo "<h2>❌ خطأ في إعداد قاعدة البيانات</h2>";
    echo "الخطأ: " . $e->getMessage() . "<br>";
    echo "<h3>تأكد من:</h3>";
    echo "<ul>";
    echo "<li>تشغيل XAMPP</li>";
    echo "<li>تشغيل خدمة MySQL</li>";
    echo "<li>صحة بيانات الاتصال</li>";
    echo "</ul>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
    direction: rtl;
}
h1, h2, h3 {
    color: #333;
}
.success { color: #28a745; }
.warning { color: #ffc107; }
.error { color: #dc3545; }
</style>
