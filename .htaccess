# نظام إدارة الموظفين - إعدادات Apache مبسطة
# Employee Management System - Simplified Apache Configuration

# تعطيل إعادة الكتابة مؤقتاً لتجنب المشاكل
# RewriteEngine On

# منع الوصول للملفات الحساسة
<Files "*.sql">
    Order allow,deny
    Deny from all
</Files>

<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

<Files ".env">
    Order allow,deny
    Deny from all
</Files>

# حماية مجلد config
<Directory "config">
    Order allow,deny
    Deny from all
</Directory>

# ضغط الملفات لتحسين الأداء
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تخزين مؤقت للملفات الثابتة
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/ico "access plus 1 month"
    ExpiresByType image/icon "access plus 1 month"
    ExpiresByType text/x-icon "access plus 1 month"
    ExpiresByType application/x-icon "access plus 1 month"
    ExpiresByType font/woff "access plus 1 month"
    ExpiresByType font/woff2 "access plus 1 month"
</IfModule>

# إعدادات الأمان
<IfModule mod_headers.c>
    # منع clickjacking
    Header always append X-Frame-Options SAMEORIGIN
    
    # منع MIME type sniffing
    Header set X-Content-Type-Options nosniff
    
    # تفعيل XSS protection
    Header set X-XSS-Protection "1; mode=block"
    
    # إخفاء معلومات الخادم
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# إعدادات PHP
<IfModule mod_php7.c>
    # تحديد حد الذاكرة
    php_value memory_limit 256M
    
    # تحديد حجم الملفات المرفوعة
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    
    # تحديد وقت التنفيذ
    php_value max_execution_time 300
    
    # تفعيل عرض الأخطاء في بيئة التطوير
    # php_flag display_errors On
    # php_flag display_startup_errors On
    # php_value error_reporting "E_ALL"
    
    # إخفاء الأخطاء في بيئة الإنتاج
    php_flag display_errors Off
    php_flag display_startup_errors Off
    php_value error_reporting 0
    
    # تحديد المنطقة الزمنية
    php_value date.timezone "Africa/Cairo"
    
    # إعدادات الجلسة
    php_value session.gc_maxlifetime 3600
    php_value session.cookie_lifetime 0
    php_flag session.cookie_secure Off
    php_flag session.cookie_httponly On
</IfModule>

# إعادة توجيه للصفحة الرئيسية إذا لم يتم تحديد ملف
DirectoryIndex index.php index.html

# منع عرض محتويات المجلدات
Options -Indexes

# إعدادات الترميز
AddDefaultCharset UTF-8

# إعادة توجيه الأخطاء الشائعة
ErrorDocument 404 /error.php?code=404
ErrorDocument 403 /error.php?code=403
ErrorDocument 500 /error.php?code=500

# قواعد إعادة الكتابة معطلة مؤقتاً
# RewriteRule ^employee/([0-9]+)/?$ pages/view_employee.php?id=$1 [L,QSA]
# RewriteRule ^employees/?$ pages/view_employees.php [L,QSA]
# RewriteRule ^add-employee/?$ pages/add_employee.php [L,QSA]
# RewriteRule ^edit-employee/([0-9]+)/?$ pages/edit_employee.php?id=$1 [L,QSA]
# RewriteRule ^search/?$ pages/search.php [L,QSA]
# RewriteRule ^reports/?$ pages/reports.php [L,QSA]

# منع الوصول المباشر للملفات الحساسة معطل مؤقتاً
# RewriteRule ^includes/ - [F,L]
# RewriteRule ^config/ - [F,L]

# إعادة توجيه HTTP إلى HTTPS (في بيئة الإنتاج)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
