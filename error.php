<?php
/**
 * صفحة الأخطاء
 * Error Page
 */

$error_code = isset($_GET['code']) ? (int)$_GET['code'] : 404;

$errors = [
    403 => [
        'title' => 'ممنوع الوصول',
        'message' => 'عذراً، ليس لديك صلاحية للوصول إلى هذه الصفحة.',
        'icon' => 'fas fa-ban'
    ],
    404 => [
        'title' => 'الصفحة غير موجودة',
        'message' => 'عذراً، الصفحة التي تبحث عنها غير موجودة.',
        'icon' => 'fas fa-search'
    ],
    500 => [
        'title' => 'خطأ في الخادم',
        'message' => 'عذراً، حدث خطأ في الخادم. يرجى المحاولة لاحقاً.',
        'icon' => 'fas fa-server'
    ]
];

$error = isset($errors[$error_code]) ? $errors[$error_code] : $errors[404];

// تحديد رمز الاستجابة HTTP
http_response_code($error_code);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $error['title']; ?> - نظام إدارة الموظفين</title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .error-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 60px 40px;
            text-align: center;
            max-width: 500px;
            width: 100%;
            margin: 20px;
        }
        
        .error-icon {
            font-size: 5rem;
            color: #667eea;
            margin-bottom: 30px;
        }
        
        .error-code {
            font-size: 4rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 20px;
        }
        
        .error-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }
        
        .error-message {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .btn-home {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            color: white;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-home:hover {
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-back {
            background: transparent;
            border: 2px solid #667eea;
            border-radius: 10px;
            padding: 10px 25px;
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            margin-left: 10px;
            transition: all 0.3s ease;
        }
        
        .btn-back:hover {
            background: #667eea;
            color: white;
        }
        
        .error-details {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            text-align: right;
        }
        
        .error-details h6 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .error-details ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .error-details li {
            padding: 5px 0;
            color: #666;
        }
        
        .error-details i {
            color: #667eea;
            margin-left: 10px;
            width: 20px;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">
            <i class="<?php echo $error['icon']; ?>"></i>
        </div>
        
        <div class="error-code"><?php echo $error_code; ?></div>
        
        <h1 class="error-title"><?php echo $error['title']; ?></h1>
        
        <p class="error-message"><?php echo $error['message']; ?></p>
        
        <div class="error-actions">
            <a href="index.php" class="btn-home">
                <i class="fas fa-home me-2"></i>
                العودة للرئيسية
            </a>
            
            <a href="javascript:history.back()" class="btn-back">
                <i class="fas fa-arrow-right me-2"></i>
                رجوع
            </a>
        </div>
        
        <div class="error-details">
            <h6>
                <i class="fas fa-lightbulb me-2"></i>
                اقتراحات للمساعدة:
            </h6>
            <ul>
                <?php if ($error_code == 404): ?>
                <li><i class="fas fa-check"></i>تأكد من صحة الرابط</li>
                <li><i class="fas fa-check"></i>استخدم قائمة التنقل</li>
                <li><i class="fas fa-check"></i>جرب البحث في الموقع</li>
                <?php elseif ($error_code == 403): ?>
                <li><i class="fas fa-check"></i>تأكد من تسجيل الدخول</li>
                <li><i class="fas fa-check"></i>تحقق من صلاحياتك</li>
                <li><i class="fas fa-check"></i>اتصل بالمدير</li>
                <?php else: ?>
                <li><i class="fas fa-check"></i>حاول تحديث الصفحة</li>
                <li><i class="fas fa-check"></i>تحقق من اتصال الإنترنت</li>
                <li><i class="fas fa-check"></i>اتصل بالدعم الفني</li>
                <?php endif; ?>
            </ul>
        </div>
        
        <div class="mt-4">
            <small class="text-muted">
                إذا استمرت المشكلة، يرجى الاتصال بالدعم الفني
            </small>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // إعادة توجيه تلقائي بعد 30 ثانية للصفحة الرئيسية
        setTimeout(function() {
            if (confirm('هل تريد العودة للصفحة الرئيسية؟')) {
                window.location.href = 'index.php';
            }
        }, 30000);
        
        // تسجيل الخطأ في وحدة التحكم للمطورين
        console.error('Error <?php echo $error_code; ?>: <?php echo $error['title']; ?>');
        console.info('Current URL: ' + window.location.href);
        console.info('Referrer: ' + document.referrer);
        console.info('User Agent: ' + navigator.userAgent);
    </script>
</body>
</html>
