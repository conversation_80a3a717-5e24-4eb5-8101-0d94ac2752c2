<?php
/**
 * صفحة عرض تفاصيل موظف
 * View Employee Details Page
 */

require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
requireLogin();

$employee_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (empty($employee_id)) {
    header('Location: view_employees.php?error=' . urlencode('معرف الموظف مطلوب'));
    exit();
}

$db = getDatabase();

// جلب بيانات الموظف
$employee = $db->fetch("SELECT * FROM employee_full_view WHERE id = ?", [$employee_id]);

if (!$employee) {
    header('Location: view_employees.php?error=' . urlencode('الموظف غير موجود'));
    exit();
}

$page_title = 'تفاصيل الموظف: ' . $employee['full_name'];

// جلب سجل التعديلات
$audit_logs = $db->fetchAll(
    "SELECT al.*, u.full_name as user_name 
     FROM audit_log al 
     LEFT JOIN users u ON al.user_id = u.id 
     WHERE al.employee_id = ? 
     ORDER BY al.timestamp DESC 
     LIMIT 10",
    [$employee_id]
);

include '../includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user me-2"></i>
        تفاصيل الموظف
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <?php if (hasPermission('hr_employee')): ?>
            <a href="edit_employee.php?id=<?php echo $employee['id']; ?>" class="btn btn-warning">
                <i class="fas fa-edit me-2"></i>تعديل البيانات
            </a>
            <?php endif; ?>
            <button type="button" class="btn btn-outline-secondary" onclick="printPage()">
                <i class="fas fa-print me-2"></i>طباعة
            </button>
        </div>
        <a href="view_employees.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <!-- البيانات الأساسية -->
    <div class="col-lg-8">
        <!-- البيانات الشخصية -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>
                    البيانات الشخصية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">كود الموظف</label>
                        <div class="fw-bold">
                            <span class="badge bg-primary fs-6"><?php echo $employee['employee_code']; ?></span>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">الاسم الكامل</label>
                        <div class="fw-bold fs-5"><?php echo $employee['full_name']; ?></div>
                        <?php if ($employee['nickname']): ?>
                            <small class="text-muted">(<?php echo $employee['nickname']; ?>)</small>
                        <?php endif; ?>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">الرقم القومي</label>
                        <div class="fw-bold"><?php echo $employee['national_id']; ?></div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">الرقم التأميني</label>
                        <div class="fw-bold"><?php echo $employee['insurance_number'] ?: 'غير محدد'; ?></div>
                    </div>

                    <!-- البيانات المستخرجة من الرقم القومي -->
                    <?php if ($employee['birth_date']): ?>
                    <div class="col-md-4 mb-3">
                        <label class="form-label text-muted">تاريخ الميلاد</label>
                        <div class="fw-bold">
                            <i class="fas fa-birthday-cake me-2 text-primary"></i>
                            <?php echo formatDate($employee['birth_date']); ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if ($employee['age']): ?>
                    <div class="col-md-4 mb-3">
                        <label class="form-label text-muted">العمر</label>
                        <div class="fw-bold">
                            <span class="badge bg-info fs-6"><?php echo $employee['age']; ?> سنة</span>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if ($employee['birth_governorate']): ?>
                    <div class="col-md-4 mb-3">
                        <label class="form-label text-muted">محافظة الميلاد</label>
                        <div class="fw-bold">
                            <i class="fas fa-map-marker-alt me-2 text-success"></i>
                            <?php echo $employee['birth_governorate']; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <div class="col-md-4 mb-3">
                        <label class="form-label text-muted">النوع</label>
                        <div>
                            <span class="badge <?php echo $employee['gender'] == 'ذكر' ? 'bg-info' : 'bg-warning'; ?> fs-6">
                                <?php echo $employee['gender']; ?>
                            </span>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label class="form-label text-muted">الديانة</label>
                        <div class="fw-bold"><?php echo $employee['religion'] ?: 'غير محدد'; ?></div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label class="form-label text-muted">الحالة الاجتماعية</label>
                        <div>
                            <span class="badge bg-secondary fs-6"><?php echo $employee['marital_status']; ?></span>
                        </div>
                    </div>

                    <?php if ($employee['marital_status'] != 'أعزب' && isset($employee['children_count'])): ?>
                    <div class="col-md-4 mb-3">
                        <label class="form-label text-muted">عدد الأبناء</label>
                        <div class="fw-bold">
                            <i class="fas fa-baby me-2 text-info"></i>
                            <?php echo $employee['children_count']; ?>
                            <?php if ($employee['children_count'] == 0): ?>
                                <small class="text-muted">(لا يوجد أطفال)</small>
                            <?php elseif ($employee['children_count'] == 1): ?>
                                <small class="text-muted">(طفل واحد)</small>
                            <?php elseif ($employee['children_count'] == 2): ?>
                                <small class="text-muted">(طفلان)</small>
                            <?php else: ?>
                                <small class="text-muted">(أطفال)</small>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($employee['has_disability']): ?>
                    <div class="col-md-12 mb-3">
                        <label class="form-label text-muted">الإعاقة</label>
                        <div class="alert alert-info">
                            <div class="row">
                                <div class="col-md-6">
                                    <i class="fas fa-wheelchair me-2"></i>
                                    <strong>نوع الإعاقة:</strong> <?php echo $employee['disability_type'] ?: 'غير محدد النوع'; ?>
                                </div>
                                <?php if ($employee['disability_percentage']): ?>
                                <div class="col-md-6">
                                    <i class="fas fa-percentage me-2"></i>
                                    <strong>نسبة الإعاقة:</strong>
                                    <span class="badge bg-warning text-dark fs-6"><?php echo $employee['disability_percentage']; ?>%</span>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- بيانات الاتصال والعنوان -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-map-marker-alt me-2"></i>
                    بيانات الاتصال والعنوان
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">رقم الهاتف</label>
                        <div class="fw-bold">
                            <a href="tel:<?php echo $employee['phone']; ?>" class="text-decoration-none">
                                <i class="fas fa-phone me-2"></i><?php echo $employee['phone']; ?>
                            </a>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">البريد الإلكتروني</label>
                        <div class="fw-bold">
                            <?php if ($employee['email']): ?>
                                <a href="mailto:<?php echo $employee['email']; ?>" class="text-decoration-none">
                                    <i class="fas fa-envelope me-2"></i><?php echo $employee['email']; ?>
                                </a>
                            <?php else: ?>
                                غير محدد
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="col-md-12 mb-3">
                        <label class="form-label text-muted">العنوان</label>
                        <div class="fw-bold">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            <?php 
                            $address_parts = array_filter([
                                $employee['street'],
                                $employee['city'],
                                $employee['governorate'],
                                $employee['country']
                            ]);
                            echo implode(', ', $address_parts);
                            ?>
                            <?php if ($employee['postal_code']): ?>
                                <br><small class="text-muted">الرمز البريدي: <?php echo $employee['postal_code']; ?></small>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- بيانات التجنيد والخدمة العامة -->
        <?php if ($employee['gender'] == 'ذكر' && $employee['military_status'] != 'غير محدد'): ?>
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-shield me-2"></i>
                    بيانات التجنيد
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">حالة التجنيد</label>
                        <div class="fw-bold">
                            <?php
                            $status_class = '';
                            switch($employee['military_status']) {
                                case 'قام بالتجنيد': $status_class = 'bg-success'; break;
                                case 'إعفاء نهائي': $status_class = 'bg-warning'; break;
                                case 'إعفاء مؤقت': $status_class = 'bg-info'; break;
                                case 'تأجيل التجنيد': $status_class = 'bg-secondary'; break;
                                default: $status_class = 'bg-light text-dark';
                            }
                            ?>
                            <span class="badge <?php echo $status_class; ?> fs-6">
                                <?php echo $employee['military_status']; ?>
                            </span>
                        </div>
                    </div>

                    <?php if ($employee['military_status_date']): ?>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">تاريخ المعاملة</label>
                        <div class="fw-bold"><?php echo formatDate($employee['military_status_date']); ?></div>
                    </div>
                    <?php endif; ?>

                    <?php if ($employee['military_notes']): ?>
                    <div class="col-md-12 mb-3">
                        <label class="form-label text-muted">ملاحظات التجنيد</label>
                        <div class="fw-bold"><?php echo nl2br(htmlspecialchars($employee['military_notes'])); ?></div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <?php if ($employee['gender'] == 'أنثى' && $employee['public_service_status'] != 'غير محدد'): ?>
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="fas fa-hands-helping me-2"></i>
                    بيانات الخدمة العامة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">حالة الخدمة العامة</label>
                        <div class="fw-bold">
                            <?php
                            $service_class = '';
                            switch($employee['public_service_status']) {
                                case 'تأدية الخدمة': $service_class = 'bg-success'; break;
                                case 'لم تؤدى الخدمة': $service_class = 'bg-danger'; break;
                                default: $service_class = 'bg-light text-dark';
                            }
                            ?>
                            <span class="badge <?php echo $service_class; ?> fs-6">
                                <?php echo $employee['public_service_status']; ?>
                            </span>
                        </div>
                    </div>

                    <?php if ($employee['public_service_date']): ?>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">تاريخ المعاملة</label>
                        <div class="fw-bold"><?php echo formatDate($employee['public_service_date']); ?></div>
                    </div>
                    <?php endif; ?>

                    <?php if ($employee['public_service_notes']): ?>
                    <div class="col-md-12 mb-3">
                        <label class="form-label text-muted">ملاحظات الخدمة العامة</label>
                        <div class="fw-bold"><?php echo nl2br(htmlspecialchars($employee['public_service_notes'])); ?></div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- المؤهلات العلمية -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-graduation-cap me-2"></i>
                    المؤهلات العلمية
                </h5>
            </div>
            <div class="card-body">
                <?php
                // جلب المؤهلات من الجدول الجديد
                $qualifications = $db->fetchAll("
                    SELECT * FROM employee_qualifications
                    WHERE employee_id = ?
                    ORDER BY qualification_date ASC
                ", [$employee['id']]);

                if (!empty($qualifications)): ?>
                    <div class="row">
                        <?php foreach ($qualifications as $index => $qual): ?>
                            <div class="col-md-6 mb-3">
                                <div class="card border-primary h-100">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="card-title text-primary mb-0">
                                                <i class="fas fa-graduation-cap me-2"></i><?php echo $qual['qualification_type']; ?>
                                            </h6>
                                            <span class="badge bg-primary"><?php echo $index + 1; ?></span>
                                        </div>

                                        <?php if ($qual['specialization']): ?>
                                            <p class="card-text mb-2">
                                                <i class="fas fa-book me-2 text-info"></i>
                                                <strong>التخصص:</strong> <?php echo $qual['specialization']; ?>
                                            </p>
                                        <?php endif; ?>

                                        <?php if ($qual['institution']): ?>
                                            <p class="card-text mb-2">
                                                <i class="fas fa-university me-2 text-warning"></i>
                                                <strong>المؤسسة:</strong> <?php echo $qual['institution']; ?>
                                            </p>
                                        <?php endif; ?>

                                        <?php if ($qual['grade']): ?>
                                            <p class="card-text mb-2">
                                                <i class="fas fa-award me-2 text-success"></i>
                                                <strong>التقدير:</strong>
                                                <span class="badge bg-success"><?php echo $qual['grade']; ?></span>
                                            </p>
                                        <?php endif; ?>

                                        <p class="card-text mb-0">
                                            <i class="fas fa-calendar me-2 text-muted"></i>
                                            <small class="text-muted">
                                                <strong>تاريخ الحصول:</strong> <?php echo formatDate($qual['qualification_date']); ?>
                                            </small>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <!-- عرض المؤهل القديم إذا لم توجد مؤهلات في الجدول الجديد -->
                    <div class="row">
                        <?php if ($employee['qualification']): ?>
                            <div class="col-md-6 mb-3">
                                <div class="card border-secondary">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="fas fa-graduation-cap me-2"></i><?php echo $employee['qualification']; ?>
                                        </h6>
                                        <?php if ($employee['specialization']): ?>
                                            <p class="card-text mb-2">
                                                <i class="fas fa-book me-2 text-info"></i>
                                                <strong>التخصص:</strong> <?php echo $employee['specialization']; ?>
                                            </p>
                                        <?php endif; ?>
                                        <?php if ($employee['qualification_date']): ?>
                                            <p class="card-text mb-0">
                                                <i class="fas fa-calendar me-2 text-muted"></i>
                                                <small class="text-muted">
                                                    <strong>تاريخ الحصول:</strong> <?php echo formatDate($employee['qualification_date']); ?>
                                                </small>
                                            </p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <?php if ($employee['highest_qualification'] && $employee['highest_qualification'] != $employee['qualification']): ?>
                                <div class="col-md-6 mb-3">
                                    <div class="card border-warning">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <i class="fas fa-star me-2"></i><?php echo $employee['highest_qualification']; ?>
                                                <span class="badge bg-warning text-dark ms-2">الأعلى</span>
                                            </h6>
                                            <?php if ($employee['highest_qualification_date']): ?>
                                                <p class="card-text mb-0">
                                                    <i class="fas fa-calendar me-2 text-muted"></i>
                                                    <small class="text-muted">
                                                        <strong>تاريخ الحصول:</strong> <?php echo formatDate($employee['highest_qualification_date']); ?>
                                                    </small>
                                                </p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="col-12">
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-graduation-cap fa-3x mb-3"></i>
                                    <p>لم يتم تحديد أي مؤهلات علمية</p>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- الشريط الجانبي -->
    <div class="col-lg-4">
        <!-- معلومات سريعة -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="text-muted">تاريخ الإضافة:</span>
                    <span class="fw-bold"><?php echo formatDate($employee['created_at']); ?></span>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="text-muted">آخر تحديث:</span>
                    <span class="fw-bold"><?php echo formatDate($employee['updated_at']); ?></span>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="text-muted">أضيف بواسطة:</span>
                    <span class="fw-bold"><?php echo $employee['created_by_name'] ?: 'غير محدد'; ?></span>
                </div>
                
                <?php if ($employee['updated_by_name']): ?>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="text-muted">آخر تعديل بواسطة:</span>
                    <span class="fw-bold"><?php echo $employee['updated_by_name']; ?></span>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- إجراءات سريعة -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <?php if (hasPermission('hr_employee')): ?>
                    <a href="edit_employee.php?id=<?php echo $employee['id']; ?>" class="btn btn-warning btn-sm">
                        <i class="fas fa-edit me-2"></i>تعديل البيانات
                    </a>
                    <?php endif; ?>
                    
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="printPage()">
                        <i class="fas fa-print me-2"></i>طباعة البيانات
                    </button>
                    
                    <a href="view_employees.php" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-list me-2"></i>عرض جميع الموظفين
                    </a>
                    
                    <?php if (hasPermission('hr_manager')): ?>
                    <button type="button" 
                            class="btn btn-outline-danger btn-sm" 
                            onclick="deleteEmployee(<?php echo $employee['id']; ?>, '<?php echo $employee['full_name']; ?>')">
                        <i class="fas fa-trash me-2"></i>حذف الموظف
                    </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- سجل التعديلات -->
        <?php if (!empty($audit_logs) && hasPermission('hr_manager')): ?>
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    آخر التعديلات
                </h6>
            </div>
            <div class="card-body">
                <?php foreach ($audit_logs as $log): ?>
                <div class="d-flex align-items-start mb-3">
                    <div class="flex-shrink-0">
                        <?php
                        $icon = 'fas fa-edit text-warning';
                        if ($log['action'] == 'INSERT') $icon = 'fas fa-plus text-success';
                        if ($log['action'] == 'DELETE') $icon = 'fas fa-trash text-danger';
                        ?>
                        <i class="<?php echo $icon; ?>"></i>
                    </div>
                    <div class="flex-grow-1 ms-2">
                        <div class="fw-bold">
                            <?php
                            switch ($log['action']) {
                                case 'INSERT': echo 'إضافة موظف'; break;
                                case 'UPDATE': echo 'تعديل بيانات'; break;
                                case 'DELETE': echo 'حذف موظف'; break;
                            }
                            ?>
                        </div>
                        <small class="text-muted">
                            بواسطة: <?php echo $log['user_name']; ?><br>
                            <?php echo formatDateTime($log['timestamp']); ?>
                        </small>
                    </div>
                </div>
                <?php endforeach; ?>
                
                <div class="text-center">
                    <a href="audit_log.php?employee_id=<?php echo $employee['id']; ?>" class="btn btn-outline-primary btn-sm">
                        عرض السجل الكامل
                    </a>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<script>
// حذف موظف
function deleteEmployee(id, name) {
    if (confirm('هل أنت متأكد من حذف الموظف: ' + name + '؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
        fetch('delete_employee.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({id: id})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = 'view_employees.php?success=' + encodeURIComponent(data.message);
            } else {
                alert(data.message || 'حدث خطأ أثناء الحذف');
            }
        })
        .catch(error => {
            alert('خطأ في الاتصال');
            console.error('Error:', error);
        });
    }
}
</script>

<?php include '../includes/footer.php'; ?>
