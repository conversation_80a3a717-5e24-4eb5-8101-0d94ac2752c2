<?php
/**
 * صفحة عرض تفاصيل موظف
 * View Employee Details Page
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
requireLogin();

$employee_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (empty($employee_id)) {
    header('Location: view_employees.php?error=' . urlencode('معرف الموظف مطلوب'));
    exit();
}

$db = getDatabase();

// جلب بيانات الموظف
$employee = $db->fetchOne("SELECT * FROM employee_full_view WHERE id = ?", [$employee_id]);

if (!$employee) {
    header('Location: view_employees.php?error=' . urlencode('الموظف غير موجود'));
    exit();
}

$page_title = 'تفاصيل الموظف: ' . $employee['full_name'];

// جلب سجل التعديلات
$audit_logs = $db->fetchAll(
    "SELECT al.*, u.full_name as user_name 
     FROM audit_log al 
     LEFT JOIN users u ON al.user_id = u.id 
     WHERE al.employee_id = ? 
     ORDER BY al.timestamp DESC 
     LIMIT 10",
    [$employee_id]
);

include '../includes/header.php';
?>

<!-- Header Section -->
<div class="employee-header mb-4">
    <div class="header-background"></div>
    <div class="header-content">
        <div class="d-flex align-items-center justify-content-between">
            <div class="d-flex align-items-center">
                <div class="employee-avatar-large me-4">
                    <?php if ($employee['photo'] && file_exists('uploads/employee_photos/' . $employee['photo'])): ?>
                        <img src="uploads/employee_photos/<?php echo $employee['photo']; ?>"
                             alt="صورة <?php echo $employee['full_name']; ?>"
                             class="avatar-img">
                    <?php else: ?>
                        <div class="avatar-placeholder">
                            <i class="fas fa-user"></i>
                        </div>
                    <?php endif; ?>
                    <div class="status-indicator"></div>
                </div>

                <div class="employee-title">
                    <h1 class="employee-name"><?php echo $employee['full_name']; ?></h1>
                    <div class="employee-meta">
                        <span class="employee-code">
                            <i class="fas fa-id-badge me-1"></i>
                            <?php echo $employee['employee_code']; ?>
                        </span>
                        <span class="employee-department">
                            <i class="fas fa-building me-1"></i>
                            <?php echo $employee['qualification']; ?>
                        </span>
                        <?php if ($employee['specialization']): ?>
                        <span class="employee-position">
                            <i class="fas fa-briefcase me-1"></i>
                            <?php echo $employee['specialization']; ?>
                        </span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="header-actions">
                <?php if (hasPermission('hr_employee')): ?>
                <a href="edit_employee.php?id=<?php echo $employee['id']; ?>" class="btn btn-primary btn-action">
                    <i class="fas fa-edit me-2"></i>تعديل البيانات
                </a>
                <?php endif; ?>
                <button type="button" class="btn btn-outline-light btn-action" onclick="printPage()">
                    <i class="fas fa-print me-2"></i>طباعة
                </button>
                <a href="view_employees.php" class="btn btn-outline-light btn-action">
                    <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- البيانات الأساسية -->
    <div class="col-lg-8">
        <!-- البيانات الشخصية -->
        <div class="card modern-card mb-4" data-aos="fade-up" data-aos-delay="100">
            <div class="card-header modern-header">
                <div class="d-flex align-items-center">
                    <div class="header-icon">
                        <i class="fas fa-user"></i>
                    </div>
                    <h5 class="card-title mb-0">البيانات الشخصية</h5>
                </div>
            </div>
            <div class="card-body modern-body">
                <div class="info-grid">
                    <div class="info-item" data-aos="fade-right" data-aos-delay="200">
                        <div class="info-icon">
                            <i class="fas fa-id-badge"></i>
                        </div>
                        <div class="info-content">
                            <label class="info-label">كود الموظف</label>
                            <div class="info-value">
                                <span class="employee-badge"><?php echo $employee['employee_code']; ?></span>
                            </div>
                        </div>
                    </div>

                    <div class="info-item" data-aos="fade-right" data-aos-delay="300">
                        <div class="info-icon">
                            <i class="fas fa-user-circle"></i>
                        </div>
                        <div class="info-content">
                            <label class="info-label">الاسم الكامل</label>
                            <div class="info-value main-name"><?php echo $employee['full_name']; ?></div>
                            <?php if ($employee['nickname']): ?>
                                <small class="nickname">(<?php echo $employee['nickname']; ?>)</small>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="info-item" data-aos="fade-right" data-aos-delay="400">
                        <div class="info-icon">
                            <i class="fas fa-id-card"></i>
                        </div>
                        <div class="info-content">
                            <label class="info-label">الرقم القومي</label>
                            <div class="info-value"><?php echo $employee['national_id']; ?></div>
                            <?php if ($employee['national_id_expiry_date']): ?>
                                <?php
                                $expiry_date = new DateTime($employee['national_id_expiry_date']);
                                $today = new DateTime();
                                $days_until_expiry = $today->diff($expiry_date)->days;
                                $is_expired = $expiry_date < $today;
                                ?>
                                <div class="expiry-info">
                                    <i class="fas fa-calendar me-1"></i>
                                    ينتهي في: <?php echo formatDate($employee['national_id_expiry_date']); ?>
                                    <?php if ($is_expired): ?>
                                        <span class="status-badge expired">منتهي الصلاحية</span>
                                    <?php elseif ($days_until_expiry <= 30): ?>
                                        <span class="status-badge warning">ينتهي قريباً</span>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">الرقم القومي</label>
                        <div class="fw-bold"><?php echo $employee['national_id']; ?></div>
                        <?php if ($employee['national_id_expiry_date']): ?>
                            <?php
                            $expiry_date = new DateTime($employee['national_id_expiry_date']);
                            $today = new DateTime();
                            $days_until_expiry = $today->diff($expiry_date)->days;
                            $is_expired = $expiry_date < $today;
                            ?>
                            <small class="<?php echo $is_expired ? 'text-danger' : ($days_until_expiry <= 30 ? 'text-warning' : 'text-muted'); ?>">
                                <i class="fas fa-calendar me-1"></i>
                                ينتهي في: <?php echo formatDate($employee['national_id_expiry_date']); ?>
                                <?php if ($is_expired): ?>
                                    <span class="badge bg-danger ms-1">منتهي الصلاحية</span>
                                <?php elseif ($days_until_expiry <= 30): ?>
                                    <span class="badge bg-warning text-dark ms-1">ينتهي قريباً</span>
                                <?php endif; ?>
                            </small>
                        <?php endif; ?>
                    </div>

                    <?php if ($employee['national_id_photo']): ?>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">صورة الرقم القومي</label>
                        <div>
                            <?php
                            $file_path = 'uploads/national_id_photos/' . $employee['national_id_photo'];
                            $file_extension = strtolower(pathinfo($employee['national_id_photo'], PATHINFO_EXTENSION));
                            ?>
                            <?php if (file_exists($file_path)): ?>
                                <?php if ($file_extension === 'pdf'): ?>
                                    <a href="<?php echo $file_path; ?>" target="_blank" class="btn btn-outline-danger btn-sm">
                                        <i class="fas fa-file-pdf me-1"></i>عرض PDF
                                    </a>
                                <?php else: ?>
                                    <img src="<?php echo $file_path; ?>"
                                         alt="صورة الرقم القومي"
                                         class="img-thumbnail"
                                         style="max-width: 150px; max-height: 100px; cursor: pointer;"
                                         onclick="showNationalIdModal('<?php echo $file_path; ?>', '<?php echo $employee['full_name']; ?>')">
                                <?php endif; ?>
                            <?php else: ?>
                                <small class="text-muted">الملف غير موجود</small>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">الرقم التأميني</label>
                        <div class="fw-bold"><?php echo $employee['insurance_number'] ?: 'غير محدد'; ?></div>
                    </div>

                    <!-- البيانات المستخرجة من الرقم القومي -->
                    <?php if ($employee['birth_date']): ?>
                    <div class="col-md-4 mb-3">
                        <label class="form-label text-muted">تاريخ الميلاد</label>
                        <div class="fw-bold">
                            <i class="fas fa-birthday-cake me-2 text-primary"></i>
                            <?php echo formatDate($employee['birth_date']); ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if ($employee['age']): ?>
                    <div class="col-md-4 mb-3">
                        <label class="form-label text-muted">العمر</label>
                        <div class="fw-bold">
                            <span class="badge bg-info fs-6"><?php echo $employee['age']; ?> سنة</span>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if ($employee['birth_governorate']): ?>
                    <div class="col-md-4 mb-3">
                        <label class="form-label text-muted">محافظة الميلاد</label>
                        <div class="fw-bold">
                            <i class="fas fa-map-marker-alt me-2 text-success"></i>
                            <?php echo $employee['birth_governorate']; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <div class="col-md-4 mb-3">
                        <label class="form-label text-muted">النوع</label>
                        <div>
                            <span class="badge <?php echo $employee['gender'] == 'ذكر' ? 'bg-info' : 'bg-warning'; ?> fs-6">
                                <?php echo $employee['gender']; ?>
                            </span>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label class="form-label text-muted">الديانة</label>
                        <div class="fw-bold"><?php echo $employee['religion'] ?: 'غير محدد'; ?></div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label class="form-label text-muted">الحالة الاجتماعية</label>
                        <div>
                            <span class="badge bg-secondary fs-6"><?php echo $employee['marital_status']; ?></span>
                        </div>
                    </div>

                    <?php if ($employee['marital_status'] != 'أعزب' && isset($employee['children_count'])): ?>
                    <div class="col-md-4 mb-3">
                        <label class="form-label text-muted">عدد الأبناء</label>
                        <div class="fw-bold">
                            <i class="fas fa-baby me-2 text-info"></i>
                            <?php echo $employee['children_count']; ?>
                            <?php if ($employee['children_count'] == 0): ?>
                                <small class="text-muted">(لا يوجد أطفال)</small>
                            <?php elseif ($employee['children_count'] == 1): ?>
                                <small class="text-muted">(طفل واحد)</small>
                            <?php elseif ($employee['children_count'] == 2): ?>
                                <small class="text-muted">(طفلان)</small>
                            <?php else: ?>
                                <small class="text-muted">(أطفال)</small>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($employee['has_disability']): ?>
                    <div class="col-md-12 mb-3">
                        <label class="form-label text-muted">الإعاقة</label>
                        <div class="alert alert-info">
                            <div class="row">
                                <div class="col-md-6">
                                    <i class="fas fa-wheelchair me-2"></i>
                                    <strong>نوع الإعاقة:</strong> <?php echo $employee['disability_type'] ?: 'غير محدد النوع'; ?>
                                </div>
                                <?php if ($employee['disability_percentage']): ?>
                                <div class="col-md-6">
                                    <i class="fas fa-percentage me-2"></i>
                                    <strong>نسبة الإعاقة:</strong>
                                    <span class="badge bg-warning text-dark fs-6"><?php echo $employee['disability_percentage']; ?>%</span>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- بيانات الاتصال والعنوان -->
        <div class="card modern-card mb-4" data-aos="fade-up" data-aos-delay="500">
            <div class="card-header modern-header">
                <div class="d-flex align-items-center">
                    <div class="header-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <h5 class="card-title mb-0">بيانات الاتصال والعنوان</h5>
                </div>
            </div>
            <div class="card-body modern-body">
                <div class="info-grid">
                    <div class="info-item contact-item" data-aos="fade-right" data-aos-delay="600">
                        <div class="info-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="info-content">
                            <label class="info-label">رقم الهاتف</label>
                            <div class="info-value">
                                <a href="tel:<?php echo $employee['phone']; ?>" class="contact-link">
                                    <?php echo $employee['phone']; ?>
                                </a>
                            </div>
                        </div>
                        <div class="contact-action">
                            <a href="tel:<?php echo $employee['phone']; ?>" class="action-btn call-btn">
                                <i class="fas fa-phone"></i>
                            </a>
                        </div>
                    </div>

                    <div class="info-item contact-item" data-aos="fade-right" data-aos-delay="700">
                        <div class="info-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="info-content">
                            <label class="info-label">البريد الإلكتروني</label>
                            <div class="info-value">
                                <?php if ($employee['email']): ?>
                                    <a href="mailto:<?php echo $employee['email']; ?>" class="contact-link">
                                        <?php echo $employee['email']; ?>
                                    </a>
                                <?php else: ?>
                                    <span class="text-muted">غير محدد</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php if ($employee['email']): ?>
                        <div class="contact-action">
                            <a href="mailto:<?php echo $employee['email']; ?>" class="action-btn email-btn">
                                <i class="fas fa-envelope"></i>
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="info-item address-item" data-aos="fade-right" data-aos-delay="800">
                        <div class="info-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="info-content">
                            <label class="info-label">العنوان الكامل</label>
                            <div class="info-value">
                                <?php
                                $address_parts = array_filter([
                                    $employee['street'],
                                    $employee['city'],
                                    $employee['governorate'],
                                    $employee['country']
                                ]);
                                echo implode(', ', $address_parts);
                                ?>
                                <?php if ($employee['postal_code']): ?>
                                    <div class="postal-code">
                                        <i class="fas fa-mail-bulk me-1"></i>
                                        الرمز البريدي: <?php echo $employee['postal_code']; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="contact-action">
                            <a href="https://maps.google.com/?q=<?php echo urlencode(implode(', ', $address_parts)); ?>"
                               target="_blank" class="action-btn map-btn">
                                <i class="fas fa-map"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- بيانات التجنيد والخدمة العامة -->
        <?php if ($employee['gender'] == 'ذكر' && $employee['military_status'] != 'غير محدد'): ?>
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-shield me-2"></i>
                    بيانات التجنيد
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">حالة التجنيد</label>
                        <div class="fw-bold">
                            <?php
                            $status_class = '';
                            switch($employee['military_status']) {
                                case 'قام بالتجنيد': $status_class = 'bg-success'; break;
                                case 'إعفاء نهائي': $status_class = 'bg-warning'; break;
                                case 'إعفاء مؤقت': $status_class = 'bg-info'; break;
                                case 'تأجيل التجنيد': $status_class = 'bg-secondary'; break;
                                default: $status_class = 'bg-light text-dark';
                            }
                            ?>
                            <span class="badge <?php echo $status_class; ?> fs-6">
                                <?php echo $employee['military_status']; ?>
                            </span>
                        </div>
                    </div>

                    <?php if ($employee['military_status_date']): ?>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">تاريخ المعاملة</label>
                        <div class="fw-bold"><?php echo formatDate($employee['military_status_date']); ?></div>
                    </div>
                    <?php endif; ?>

                    <?php if ($employee['service_certificate_expiry_date']): ?>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">تاريخ انتهاء شهادة التجنيد</label>
                        <div class="fw-bold">
                            <?php
                            $expiry_date = $employee['service_certificate_expiry_date'];
                            $days_until_expiry = (strtotime($expiry_date) - time()) / (60 * 60 * 24);

                            if ($days_until_expiry < 0) {
                                $badge_class = 'bg-danger';
                                $status_text = 'منتهية الصلاحية';
                            } elseif ($days_until_expiry <= 30) {
                                $badge_class = 'bg-warning text-dark';
                                $status_text = 'تنتهي قريباً';
                            } elseif ($days_until_expiry <= 90) {
                                $badge_class = 'bg-info';
                                $status_text = 'تحذير مبكر';
                            } else {
                                $badge_class = 'bg-success';
                                $status_text = 'سارية المفعول';
                            }
                            ?>
                            <?php echo formatDate($expiry_date); ?>
                            <span class="badge <?php echo $badge_class; ?> ms-2"><?php echo $status_text; ?></span>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if ($employee['service_certificate_photo']): ?>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">صورة شهادة التجنيد</label>
                        <div>
                            <?php
                            $file_path = '../uploads/service_certificate_photos/' . $employee['service_certificate_photo'];
                            $file_extension = strtolower(pathinfo($employee['service_certificate_photo'], PATHINFO_EXTENSION));
                            ?>

                            <?php if ($file_extension === 'pdf'): ?>
                                <!-- عرض PDF -->
                                <div class="d-flex align-items-center p-2 border rounded">
                                    <i class="fas fa-file-pdf fa-2x text-danger me-3"></i>
                                    <div class="flex-grow-1">
                                        <strong>شهادة التجنيد</strong><br>
                                        <small class="text-muted">ملف PDF</small>
                                    </div>
                                    <div>
                                        <a href="<?php echo $file_path; ?>" target="_blank" class="btn btn-sm btn-outline-primary me-1">
                                            <i class="fas fa-eye"></i> عرض
                                        </a>
                                        <a href="<?php echo $file_path; ?>" download class="btn btn-sm btn-outline-success">
                                            <i class="fas fa-download"></i> تحميل
                                        </a>
                                    </div>
                                </div>
                            <?php else: ?>
                                <!-- عرض الصورة -->
                                <img src="<?php echo $file_path; ?>"
                                     alt="شهادة التجنيد"
                                     class="img-thumbnail"
                                     style="max-width: 200px; max-height: 150px; cursor: pointer;"
                                     onclick="showImageModal('<?php echo $file_path; ?>', 'شهادة التجنيد')">
                                <div class="mt-2">
                                    <a href="<?php echo $file_path; ?>" target="_blank" class="btn btn-sm btn-outline-primary me-1">
                                        <i class="fas fa-expand"></i> تكبير
                                    </a>
                                    <a href="<?php echo $file_path; ?>" download class="btn btn-sm btn-outline-success">
                                        <i class="fas fa-download"></i> تحميل
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if ($employee['military_notes']): ?>
                    <div class="col-md-12 mb-3">
                        <label class="form-label text-muted">ملاحظات التجنيد</label>
                        <div class="fw-bold"><?php echo nl2br(htmlspecialchars($employee['military_notes'])); ?></div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <?php if ($employee['gender'] == 'أنثى' && $employee['public_service_status'] != 'غير محدد'): ?>
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="fas fa-hands-helping me-2"></i>
                    بيانات الخدمة العامة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">حالة الخدمة العامة</label>
                        <div class="fw-bold">
                            <?php
                            $service_class = '';
                            switch($employee['public_service_status']) {
                                case 'تأدية الخدمة': $service_class = 'bg-success'; break;
                                case 'لم تؤدى الخدمة': $service_class = 'bg-danger'; break;
                                default: $service_class = 'bg-light text-dark';
                            }
                            ?>
                            <span class="badge <?php echo $service_class; ?> fs-6">
                                <?php echo $employee['public_service_status']; ?>
                            </span>
                        </div>
                    </div>

                    <?php if ($employee['public_service_date']): ?>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">تاريخ المعاملة</label>
                        <div class="fw-bold"><?php echo formatDate($employee['public_service_date']); ?></div>
                    </div>
                    <?php endif; ?>

                    <?php if ($employee['service_certificate_expiry_date']): ?>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">تاريخ انتهاء شهادة الخدمة العامة</label>
                        <div class="fw-bold">
                            <?php
                            $expiry_date = $employee['service_certificate_expiry_date'];
                            $days_until_expiry = (strtotime($expiry_date) - time()) / (60 * 60 * 24);

                            if ($days_until_expiry < 0) {
                                $badge_class = 'bg-danger';
                                $status_text = 'منتهية الصلاحية';
                            } elseif ($days_until_expiry <= 30) {
                                $badge_class = 'bg-warning text-dark';
                                $status_text = 'تنتهي قريباً';
                            } elseif ($days_until_expiry <= 90) {
                                $badge_class = 'bg-info';
                                $status_text = 'تحذير مبكر';
                            } else {
                                $badge_class = 'bg-success';
                                $status_text = 'سارية المفعول';
                            }
                            ?>
                            <?php echo formatDate($expiry_date); ?>
                            <span class="badge <?php echo $badge_class; ?> ms-2"><?php echo $status_text; ?></span>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if ($employee['service_certificate_photo']): ?>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">صورة شهادة الخدمة العامة</label>
                        <div>
                            <?php
                            $file_path = '../uploads/service_certificate_photos/' . $employee['service_certificate_photo'];
                            $file_extension = strtolower(pathinfo($employee['service_certificate_photo'], PATHINFO_EXTENSION));
                            ?>

                            <?php if ($file_extension === 'pdf'): ?>
                                <!-- عرض PDF -->
                                <div class="d-flex align-items-center p-2 border rounded">
                                    <i class="fas fa-file-pdf fa-2x text-danger me-3"></i>
                                    <div class="flex-grow-1">
                                        <strong>شهادة الخدمة العامة</strong><br>
                                        <small class="text-muted">ملف PDF</small>
                                    </div>
                                    <div>
                                        <a href="<?php echo $file_path; ?>" target="_blank" class="btn btn-sm btn-outline-primary me-1">
                                            <i class="fas fa-eye"></i> عرض
                                        </a>
                                        <a href="<?php echo $file_path; ?>" download class="btn btn-sm btn-outline-success">
                                            <i class="fas fa-download"></i> تحميل
                                        </a>
                                    </div>
                                </div>
                            <?php else: ?>
                                <!-- عرض الصورة -->
                                <img src="<?php echo $file_path; ?>"
                                     alt="شهادة الخدمة العامة"
                                     class="img-thumbnail"
                                     style="max-width: 200px; max-height: 150px; cursor: pointer;"
                                     onclick="showImageModal('<?php echo $file_path; ?>', 'شهادة الخدمة العامة')">
                                <div class="mt-2">
                                    <a href="<?php echo $file_path; ?>" target="_blank" class="btn btn-sm btn-outline-primary me-1">
                                        <i class="fas fa-expand"></i> تكبير
                                    </a>
                                    <a href="<?php echo $file_path; ?>" download class="btn btn-sm btn-outline-success">
                                        <i class="fas fa-download"></i> تحميل
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if ($employee['public_service_notes']): ?>
                    <div class="col-md-12 mb-3">
                        <label class="form-label text-muted">ملاحظات الخدمة العامة</label>
                        <div class="fw-bold"><?php echo nl2br(htmlspecialchars($employee['public_service_notes'])); ?></div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- المؤهلات العلمية -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-graduation-cap me-2"></i>
                    المؤهلات العلمية
                </h5>
            </div>
            <div class="card-body">
                <?php
                // جلب المؤهلات من الجدول الجديد
                $qualifications = $db->fetchAll("
                    SELECT * FROM employee_qualifications
                    WHERE employee_id = ?
                    ORDER BY qualification_date ASC
                ", [$employee['id']]);

                if (!empty($qualifications)): ?>
                    <div class="row">
                        <?php foreach ($qualifications as $index => $qual): ?>
                            <div class="col-md-6 mb-3">
                                <div class="card border-primary h-100">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="card-title text-primary mb-0">
                                                <i class="fas fa-graduation-cap me-2"></i><?php echo $qual['qualification_type']; ?>
                                            </h6>
                                            <span class="badge bg-primary"><?php echo $index + 1; ?></span>
                                        </div>

                                        <?php if ($qual['specialization']): ?>
                                            <p class="card-text mb-2">
                                                <i class="fas fa-book me-2 text-info"></i>
                                                <strong>التخصص:</strong> <?php echo $qual['specialization']; ?>
                                            </p>
                                        <?php endif; ?>

                                        <?php if ($qual['institution']): ?>
                                            <p class="card-text mb-2">
                                                <i class="fas fa-university me-2 text-warning"></i>
                                                <strong>المؤسسة:</strong> <?php echo $qual['institution']; ?>
                                            </p>
                                        <?php endif; ?>

                                        <?php if ($qual['grade']): ?>
                                            <p class="card-text mb-2">
                                                <i class="fas fa-award me-2 text-success"></i>
                                                <strong>التقدير:</strong>
                                                <span class="badge bg-success"><?php echo $qual['grade']; ?></span>
                                            </p>
                                        <?php endif; ?>

                                        <p class="card-text mb-0">
                                            <i class="fas fa-calendar me-2 text-muted"></i>
                                            <small class="text-muted">
                                                <strong>تاريخ الحصول:</strong> <?php echo formatDate($qual['qualification_date']); ?>
                                            </small>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <!-- عرض المؤهل القديم إذا لم توجد مؤهلات في الجدول الجديد -->
                    <div class="row">
                        <?php if ($employee['qualification']): ?>
                            <div class="col-md-6 mb-3">
                                <div class="card border-secondary">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="fas fa-graduation-cap me-2"></i><?php echo $employee['qualification']; ?>
                                        </h6>
                                        <?php if ($employee['specialization']): ?>
                                            <p class="card-text mb-2">
                                                <i class="fas fa-book me-2 text-info"></i>
                                                <strong>التخصص:</strong> <?php echo $employee['specialization']; ?>
                                            </p>
                                        <?php endif; ?>
                                        <?php if ($employee['qualification_date']): ?>
                                            <p class="card-text mb-0">
                                                <i class="fas fa-calendar me-2 text-muted"></i>
                                                <small class="text-muted">
                                                    <strong>تاريخ الحصول:</strong> <?php echo formatDate($employee['qualification_date']); ?>
                                                </small>
                                            </p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <?php if ($employee['highest_qualification'] && $employee['highest_qualification'] != $employee['qualification']): ?>
                                <div class="col-md-6 mb-3">
                                    <div class="card border-warning">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <i class="fas fa-star me-2"></i><?php echo $employee['highest_qualification']; ?>
                                                <span class="badge bg-warning text-dark ms-2">الأعلى</span>
                                            </h6>
                                            <?php if ($employee['highest_qualification_date']): ?>
                                                <p class="card-text mb-0">
                                                    <i class="fas fa-calendar me-2 text-muted"></i>
                                                    <small class="text-muted">
                                                        <strong>تاريخ الحصول:</strong> <?php echo formatDate($employee['highest_qualification_date']); ?>
                                                    </small>
                                                </p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="col-12">
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-graduation-cap fa-3x mb-3"></i>
                                    <p>لم يتم تحديد أي مؤهلات علمية</p>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- الشريط الجانبي -->
    <div class="col-lg-4">
        <!-- معلومات سريعة -->
        <div class="card modern-card mb-4" data-aos="fade-left" data-aos-delay="200">
            <div class="card-header modern-header">
                <div class="d-flex align-items-center">
                    <div class="header-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <h6 class="card-title mb-0">معلومات سريعة</h6>
                </div>
            </div>
            <div class="card-body modern-body">
                <div class="quick-info-grid">
                    <div class="quick-info-item" data-aos="fade-left" data-aos-delay="300">
                        <div class="quick-info-icon">
                            <i class="fas fa-calendar-plus"></i>
                        </div>
                        <div class="quick-info-content">
                            <label class="quick-info-label">تاريخ الإضافة</label>
                            <div class="quick-info-value"><?php echo formatDate($employee['created_at']); ?></div>
                        </div>
                    </div>

                    <div class="quick-info-item" data-aos="fade-left" data-aos-delay="400">
                        <div class="quick-info-icon">
                            <i class="fas fa-edit"></i>
                        </div>
                        <div class="quick-info-content">
                            <label class="quick-info-label">آخر تحديث</label>
                            <div class="quick-info-value"><?php echo formatDate($employee['updated_at']); ?></div>
                        </div>
                    </div>

                    <div class="quick-info-item" data-aos="fade-left" data-aos-delay="500">
                        <div class="quick-info-icon">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="quick-info-content">
                            <label class="quick-info-label">أضيف بواسطة</label>
                            <div class="quick-info-value"><?php echo $employee['created_by_name'] ?: 'غير محدد'; ?></div>
                        </div>
                    </div>

                    <?php if ($employee['updated_by_name']): ?>
                    <div class="quick-info-item" data-aos="fade-left" data-aos-delay="600">
                        <div class="quick-info-icon">
                            <i class="fas fa-user-edit"></i>
                        </div>
                        <div class="quick-info-content">
                            <label class="quick-info-label">آخر تعديل بواسطة</label>
                            <div class="quick-info-value"><?php echo $employee['updated_by_name']; ?></div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- إجراءات سريعة -->
        <div class="card modern-card mb-4" data-aos="fade-left" data-aos-delay="700">
            <div class="card-header modern-header">
                <div class="d-flex align-items-center">
                    <div class="header-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h6 class="card-title mb-0">إجراءات سريعة</h6>
                </div>
            </div>
            <div class="card-body modern-body">
                <div class="actions-grid">
                    <?php if (hasPermission('hr_employee')): ?>
                    <a href="edit_employee.php?id=<?php echo $employee['id']; ?>"
                       class="action-card edit-action" data-aos="zoom-in" data-aos-delay="800">
                        <div class="action-icon">
                            <i class="fas fa-edit"></i>
                        </div>
                        <div class="action-content">
                            <div class="action-title">تعديل البيانات</div>
                            <div class="action-desc">تحديث معلومات الموظف</div>
                        </div>
                    </a>
                    <?php endif; ?>

                    <button type="button" class="action-card print-action" onclick="printPage()"
                            data-aos="zoom-in" data-aos-delay="900">
                        <div class="action-icon">
                            <i class="fas fa-print"></i>
                        </div>
                        <div class="action-content">
                            <div class="action-title">طباعة البيانات</div>
                            <div class="action-desc">طباعة ملف الموظف</div>
                        </div>
                    </button>

                    <a href="view_employees.php" class="action-card list-action"
                       data-aos="zoom-in" data-aos-delay="1000">
                        <div class="action-icon">
                            <i class="fas fa-list"></i>
                        </div>
                        <div class="action-content">
                            <div class="action-title">قائمة الموظفين</div>
                            <div class="action-desc">عرض جميع الموظفين</div>
                        </div>
                    </a>

                    <?php if (hasPermission('hr_manager')): ?>
                    <button type="button" class="action-card delete-action"
                            onclick="deleteEmployee(<?php echo $employee['id']; ?>, '<?php echo $employee['full_name']; ?>')"
                            data-aos="zoom-in" data-aos-delay="1100">
                        <div class="action-icon">
                            <i class="fas fa-trash"></i>
                        </div>
                        <div class="action-content">
                            <div class="action-title">حذف الموظف</div>
                            <div class="action-desc">إزالة من النظام</div>
                        </div>
                    </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- سجل التعديلات -->
        <?php if (!empty($audit_logs) && hasPermission('hr_manager')): ?>
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    آخر التعديلات
                </h6>
            </div>
            <div class="card-body">
                <?php foreach ($audit_logs as $log): ?>
                <div class="d-flex align-items-start mb-3">
                    <div class="flex-shrink-0">
                        <?php
                        $icon = 'fas fa-edit text-warning';
                        if ($log['action'] == 'INSERT') $icon = 'fas fa-plus text-success';
                        if ($log['action'] == 'DELETE') $icon = 'fas fa-trash text-danger';
                        ?>
                        <i class="<?php echo $icon; ?>"></i>
                    </div>
                    <div class="flex-grow-1 ms-2">
                        <div class="fw-bold">
                            <?php
                            switch ($log['action']) {
                                case 'INSERT': echo 'إضافة موظف'; break;
                                case 'UPDATE': echo 'تعديل بيانات'; break;
                                case 'DELETE': echo 'حذف موظف'; break;
                            }
                            ?>
                        </div>
                        <small class="text-muted">
                            بواسطة: <?php echo $log['user_name']; ?><br>
                            <?php echo formatDateTime($log['timestamp']); ?>
                        </small>
                    </div>
                </div>
                <?php endforeach; ?>
                
                <div class="text-center">
                    <a href="audit_log.php?employee_id=<?php echo $employee['id']; ?>" class="btn btn-outline-primary btn-sm">
                        عرض السجل الكامل
                    </a>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<script>
// عرض الصورة في modal
function showPhotoModal(photoSrc, employeeName) {
    // إنشاء modal ديناميكي
    const modalHtml = `
        <div class="modal fade" id="photoModal" tabindex="-1" aria-labelledby="photoModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="photoModalLabel">الصورة الشخصية - ${employeeName}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                    </div>
                    <div class="modal-body text-center">
                        <img src="${photoSrc}" alt="الصورة الشخصية" class="img-fluid rounded" style="max-height: 400px;">
                    </div>
                </div>
            </div>
        </div>
    `;

    // إزالة modal سابق إن وجد
    const existingModal = document.getElementById('photoModal');
    if (existingModal) {
        existingModal.remove();
    }

    // إضافة modal جديد
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // عرض modal
    new bootstrap.Modal(document.getElementById('photoModal')).show();
}

// عرض صورة الرقم القومي في modal
function showNationalIdModal(photoSrc, employeeName) {
    // إنشاء modal ديناميكي
    const modalHtml = `
        <div class="modal fade" id="nationalIdModal" tabindex="-1" aria-labelledby="nationalIdModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="nationalIdModalLabel">صورة الرقم القومي - ${employeeName}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                    </div>
                    <div class="modal-body text-center">
                        <img src="${photoSrc}" alt="صورة الرقم القومي" class="img-fluid rounded" style="max-height: 500px;">
                    </div>
                    <div class="modal-footer">
                        <a href="${photoSrc}" target="_blank" class="btn btn-primary">
                            <i class="fas fa-external-link-alt me-1"></i>فتح في نافذة جديدة
                        </a>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إزالة modal سابق إن وجد
    const existingModal = document.getElementById('nationalIdModal');
    if (existingModal) {
        existingModal.remove();
    }

    // إضافة modal جديد
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // عرض modal
    new bootstrap.Modal(document.getElementById('nationalIdModal')).show();
}

// حذف موظف
function deleteEmployee(id, name) {
    if (confirm('هل أنت متأكد من حذف الموظف: ' + name + '؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
        fetch('delete_employee.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({id: id})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = 'view_employees.php?success=' + encodeURIComponent(data.message);
            } else {
                alert(data.message || 'حدث خطأ أثناء الحذف');
            }
        })
        .catch(error => {
            alert('خطأ في الاتصال');
            console.error('Error:', error);
        });
    }
}
</script>

<!-- AOS Library for Animations -->
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

<style>
/* Employee Header Styles */
.employee-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

.header-content {
    padding: 1.5rem;
}

.employee-avatar-large {
    position: relative;
}

.avatar-img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid rgba(255,255,255,0.3);
}

.avatar-placeholder {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(255,255,255,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: rgba(255,255,255,0.8);
    border: 3px solid rgba(255,255,255,0.3);
}

.status-indicator {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 16px;
    height: 16px;
    background: #28a745;
    border-radius: 50%;
    border: 2px solid white;
}

.employee-name {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.employee-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.employee-meta span {
    background: rgba(255,255,255,0.2);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.85rem;
    backdrop-filter: blur(10px);
}

.btn-action {
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    margin-left: 0.25rem;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.btn-action:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(0,0,0,0.2);
}

/* Modern Card Styles */
.modern-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.modern-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.12);
}

.modern-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 1rem 1.25rem;
}

.modern-body {
    padding: 1.25rem;
}

.header-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.75rem;
    font-size: 0.9rem;
    color: white;
    flex-shrink: 0;
}

/* Info Grid Styles */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
}

.info-item {
    display: flex;
    align-items: flex-start;
    padding: 1rem;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.info-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(0,0,0,0.1);
    border-color: #667eea;
}

.info-icon {
    width: 28px;
    height: 28px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.75rem;
    font-size: 0.85rem;
    color: white;
    flex-shrink: 0;
}

.info-content {
    flex-grow: 1;
}

.info-label {
    display: block;
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
    font-weight: 500;
}

.info-value {
    font-size: 0.95rem;
    font-weight: 600;
    color: #2c3e50;
    line-height: 1.4;
}

.main-name {
    font-size: 1.1rem;
    color: #667eea;
}

.nickname {
    color: #6c757d;
    font-style: italic;
    font-size: 0.85rem;
}

.employee-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.8rem;
}

.expiry-info {
    font-size: 0.85rem;
    color: #6c757d;
    margin-top: 0.5rem;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: 0.5rem;
}

.status-badge.expired {
    background: #dc3545;
    color: white;
}

.status-badge.warning {
    background: #ffc107;
    color: #212529;
}

/* Contact Section Styles */
.contact-item {
    position: relative;
}

.contact-action {
    margin-left: 1rem;
}

.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.85rem;
}

.call-btn {
    background: #28a745;
    color: white;
}

.call-btn:hover {
    background: #218838;
    color: white;
    transform: scale(1.05);
}

.email-btn {
    background: #007bff;
    color: white;
}

.email-btn:hover {
    background: #0056b3;
    color: white;
    transform: scale(1.05);
}

.map-btn {
    background: #dc3545;
    color: white;
}

.map-btn:hover {
    background: #c82333;
    color: white;
    transform: scale(1.05);
}

.contact-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.contact-link:hover {
    color: #764ba2;
    text-decoration: underline;
}

.address-item .info-value {
    line-height: 1.6;
}

.postal-code {
    margin-top: 0.5rem;
    font-size: 0.9rem;
    color: #6c757d;
    padding: 0.25rem 0.75rem;
    background: #f8f9fa;
    border-radius: 8px;
    display: inline-block;
}

/* Quick Info Styles */
.quick-info-grid {
    display: grid;
    gap: 0.75rem;
}

.quick-info-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.quick-info-item:hover {
    transform: translateX(-2px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    border-color: #667eea;
}

.quick-info-icon {
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.5rem;
    font-size: 0.75rem;
    color: white;
    flex-shrink: 0;
}

.quick-info-content {
    flex-grow: 1;
}

.quick-info-label {
    display: block;
    font-size: 0.75rem;
    color: #6c757d;
    margin-bottom: 0.125rem;
    font-weight: 500;
}

.quick-info-value {
    font-size: 0.85rem;
    font-weight: 600;
    color: #2c3e50;
}

/* Actions Grid Styles */
.actions-grid {
    display: grid;
    gap: 0.75rem;
}

.action-card {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    cursor: pointer;
    width: 100%;
    text-align: left;
}

.action-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(0,0,0,0.12);
    text-decoration: none;
    color: inherit;
}

.edit-action:hover {
    border-color: #ffc107;
}

.print-action:hover {
    border-color: #007bff;
}

.list-action:hover {
    border-color: #6c757d;
}

.delete-action:hover {
    border-color: #dc3545;
}

.action-icon {
    width: 28px;
    height: 28px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.75rem;
    font-size: 0.85rem;
    flex-shrink: 0;
}

.edit-action .action-icon {
    background: #ffc107;
    color: white;
}

.print-action .action-icon {
    background: #007bff;
    color: white;
}

.list-action .action-icon {
    background: #6c757d;
    color: white;
}

.delete-action .action-icon {
    background: #dc3545;
    color: white;
}

.action-content {
    flex-grow: 1;
}

.action-title {
    font-size: 0.85rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.125rem;
}

.action-desc {
    font-size: 0.75rem;
    color: #6c757d;
}

/* Responsive Design */
@media (max-width: 768px) {
    .employee-header {
        margin: 0.5rem;
    }

    .header-content {
        padding: 1rem;
    }

    .d-flex.align-items-center.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }

    .employee-name {
        font-size: 1.5rem;
        text-align: center;
    }

    .employee-meta {
        justify-content: center;
    }

    .header-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        justify-content: center;
    }

    .info-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .info-item {
        padding: 0.75rem;
    }

    .modern-header {
        padding: 0.75rem 1rem;
    }

    .modern-body {
        padding: 1rem;
    }

    .header-icon {
        width: 28px;
        height: 28px;
        font-size: 0.8rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS
    AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true,
        offset: 100
    });
});

function printPage() {
    window.print();
}
</script>

<?php include '../includes/footer.php'; ?>
