<?php
/**
 * صفحة تحذيرات انتهاء صلاحية الرقم القومي
 * National ID expiry alerts page
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

$page_title = 'تحذيرات انتهاء صلاحية الرقم القومي';

// جلب الموظفين حسب حالة انتهاء الرقم القومي
$expired_employees = $db->fetchAll("
    SELECT 
        id, employee_code, first_name, family_name, national_id, national_id_expiry_date,
        DATEDIFF(national_id_expiry_date, CURDATE()) as days_until_expiry
    FROM employees 
    WHERE national_id_expiry_date IS NOT NULL 
    AND national_id_expiry_date < CURDATE()
    ORDER BY national_id_expiry_date ASC
");

$expiring_soon = $db->fetchAll("
    SELECT 
        id, employee_code, first_name, family_name, national_id, national_id_expiry_date,
        DATEDIFF(national_id_expiry_date, CURDATE()) as days_until_expiry
    FROM employees 
    WHERE national_id_expiry_date IS NOT NULL 
    AND national_id_expiry_date >= CURDATE()
    AND DATEDIFF(national_id_expiry_date, CURDATE()) <= 30
    ORDER BY national_id_expiry_date ASC
");

$expiring_later = $db->fetchAll("
    SELECT 
        id, employee_code, first_name, family_name, national_id, national_id_expiry_date,
        DATEDIFF(national_id_expiry_date, CURDATE()) as days_until_expiry
    FROM employees 
    WHERE national_id_expiry_date IS NOT NULL 
    AND DATEDIFF(national_id_expiry_date, CURDATE()) BETWEEN 31 AND 90
    ORDER BY national_id_expiry_date ASC
");

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    تحذيرات انتهاء صلاحية الرقم القومي
                </h2>
                <a href="view_employees.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة لقائمة الموظفين
                </a>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo count($expired_employees); ?></h4>
                            <p class="mb-0">منتهي الصلاحية</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-times-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo count($expiring_soon); ?></h4>
                            <p class="mb-0">ينتهي خلال 30 يوم</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo count($expiring_later); ?></h4>
                            <p class="mb-0">ينتهي خلال 90 يوم</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <?php 
                            $total_with_expiry = $db->fetchColumn("SELECT COUNT(*) FROM employees WHERE national_id_expiry_date IS NOT NULL");
                            $valid_count = $total_with_expiry - count($expired_employees) - count($expiring_soon) - count($expiring_later);
                            ?>
                            <h4><?php echo $valid_count; ?></h4>
                            <p class="mb-0">ساري المفعول</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الموظفين منتهي الصلاحية -->
    <?php if (!empty($expired_employees)): ?>
    <div class="card mb-4">
        <div class="card-header bg-danger text-white">
            <h5 class="mb-0">
                <i class="fas fa-times-circle me-2"></i>
                موظفين منتهي صلاحية الرقم القومي (<?php echo count($expired_employees); ?>)
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>كود الموظف</th>
                            <th>اسم الموظف</th>
                            <th>الرقم القومي</th>
                            <th>تاريخ الانتهاء</th>
                            <th>منتهي منذ</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($expired_employees as $employee): ?>
                        <tr class="table-danger">
                            <td><span class="badge bg-primary"><?php echo $employee['employee_code']; ?></span></td>
                            <td><?php echo $employee['first_name'] . ' ' . $employee['family_name']; ?></td>
                            <td><?php echo $employee['national_id']; ?></td>
                            <td><?php echo formatDate($employee['national_id_expiry_date']); ?></td>
                            <td>
                                <span class="badge bg-danger">
                                    <?php echo abs($employee['days_until_expiry']); ?> يوم
                                </span>
                            </td>
                            <td>
                                <a href="view_employee.php?id=<?php echo $employee['id']; ?>" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i> عرض
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- الموظفين ينتهي قريباً -->
    <?php if (!empty($expiring_soon)): ?>
    <div class="card mb-4">
        <div class="card-header bg-warning text-dark">
            <h5 class="mb-0">
                <i class="fas fa-exclamation-triangle me-2"></i>
                موظفين ينتهي الرقم القومي خلال 30 يوم (<?php echo count($expiring_soon); ?>)
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>كود الموظف</th>
                            <th>اسم الموظف</th>
                            <th>الرقم القومي</th>
                            <th>تاريخ الانتهاء</th>
                            <th>الأيام المتبقية</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($expiring_soon as $employee): ?>
                        <tr class="table-warning">
                            <td><span class="badge bg-primary"><?php echo $employee['employee_code']; ?></span></td>
                            <td><?php echo $employee['first_name'] . ' ' . $employee['family_name']; ?></td>
                            <td><?php echo $employee['national_id']; ?></td>
                            <td><?php echo formatDate($employee['national_id_expiry_date']); ?></td>
                            <td>
                                <span class="badge bg-warning text-dark">
                                    <?php echo $employee['days_until_expiry']; ?> يوم
                                </span>
                            </td>
                            <td>
                                <a href="view_employee.php?id=<?php echo $employee['id']; ?>" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i> عرض
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- الموظفين تحذير مبكر -->
    <?php if (!empty($expiring_later)): ?>
    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0">
                <i class="fas fa-clock me-2"></i>
                موظفين ينتهي الرقم القومي خلال 90 يوم (<?php echo count($expiring_later); ?>)
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>كود الموظف</th>
                            <th>اسم الموظف</th>
                            <th>الرقم القومي</th>
                            <th>تاريخ الانتهاء</th>
                            <th>الأيام المتبقية</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($expiring_later as $employee): ?>
                        <tr class="table-info">
                            <td><span class="badge bg-primary"><?php echo $employee['employee_code']; ?></span></td>
                            <td><?php echo $employee['first_name'] . ' ' . $employee['family_name']; ?></td>
                            <td><?php echo $employee['national_id']; ?></td>
                            <td><?php echo formatDate($employee['national_id_expiry_date']); ?></td>
                            <td>
                                <span class="badge bg-info">
                                    <?php echo $employee['days_until_expiry']; ?> يوم
                                </span>
                            </td>
                            <td>
                                <a href="view_employee.php?id=<?php echo $employee['id']; ?>" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i> عرض
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- رسالة عدم وجود تحذيرات -->
    <?php if (empty($expired_employees) && empty($expiring_soon) && empty($expiring_later)): ?>
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
            <h4>لا توجد تحذيرات</h4>
            <p class="text-muted">جميع الأرقام القومية سارية المفعول</p>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php include '../includes/footer.php'; ?>
