<?php
/**
 * صفحة تسجيل الخروج
 * Logout Page
 */

require_once 'includes/config.php';
require_once 'includes/functions.php';

// بدء الجلسة
startSession();

// تسجيل نشاط تسجيل الخروج
if (isset($_SESSION['user_id'])) {
    logActivity('logout', 'User logged out', $_SESSION['user_id']);
}

// مسح جميع متغيرات الجلسة
$_SESSION = array();

// حذف ملف تعريف الارتباط للجلسة إذا كان موجوداً
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// تدمير الجلسة
session_destroy();

// إعادة توجيه إلى صفحة تسجيل الدخول
header('Location: login.php?success=' . urlencode('تم تسجيل الخروج بنجاح'));
exit();
?>
