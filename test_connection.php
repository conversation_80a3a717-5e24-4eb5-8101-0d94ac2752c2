<?php
/**
 * ملف اختبار الاتصال بقاعدة البيانات
 * Database Connection Test File
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>اختبار اتصال قاعدة البيانات</h2>";
echo "<hr>";

// اختبار اتصال MySQL الأساسي
echo "<h3>1. اختبار اتصال MySQL الأساسي:</h3>";
try {
    $pdo = new PDO("mysql:host=localhost", "root", "");
    echo "✅ الاتصال بـ MySQL نجح<br>";
    
    // عرض قواعد البيانات الموجودة
    $stmt = $pdo->query("SHOW DATABASES");
    $databases = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<strong>قواعد البيانات الموجودة:</strong><br>";
    foreach ($databases as $db) {
        echo "- " . $db . "<br>";
    }
    
} catch (PDOException $e) {
    echo "❌ فشل الاتصال بـ MySQL: " . $e->getMessage() . "<br>";
}

echo "<hr>";

// اختبار وجود قاعدة البيانات المطلوبة
echo "<h3>2. اختبار قاعدة البيانات employee_management:</h3>";
try {
    $pdo = new PDO("mysql:host=localhost;dbname=employee_management", "root", "");
    echo "✅ قاعدة البيانات employee_management موجودة<br>";
    
    // اختبار الجداول
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "⚠️ قاعدة البيانات فارغة - لا توجد جداول<br>";
        echo "<strong>تحتاج لاستيراد ملف database.sql</strong><br>";
    } else {
        echo "<strong>الجداول الموجودة:</strong><br>";
        foreach ($tables as $table) {
            echo "- " . $table . "<br>";
        }
    }
    
} catch (PDOException $e) {
    echo "❌ قاعدة البيانات employee_management غير موجودة<br>";
    echo "الخطأ: " . $e->getMessage() . "<br>";
    echo "<strong>تحتاج لإنشاء قاعدة البيانات أولاً</strong><br>";
}

echo "<hr>";

// اختبار إصدار PHP
echo "<h3>3. معلومات PHP:</h3>";
echo "إصدار PHP: " . phpversion() . "<br>";
echo "إصدار MySQL: ";
try {
    $pdo = new PDO("mysql:host=localhost", "root", "");
    $version = $pdo->query('SELECT VERSION()')->fetchColumn();
    echo $version . "<br>";
} catch (PDOException $e) {
    echo "غير متاح<br>";
}

echo "<hr>";

// اختبار الملفات المطلوبة
echo "<h3>4. اختبار الملفات:</h3>";
$required_files = [
    'config/database.php',
    'includes/functions.php',
    'includes/header.php',
    'includes/footer.php',
    'database.sql'
];

foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "✅ " . $file . " موجود<br>";
    } else {
        echo "❌ " . $file . " مفقود<br>";
    }
}

echo "<hr>";

// حلول مقترحة
echo "<h3>5. الحلول المقترحة:</h3>";
echo "<ol>";
echo "<li><strong>إنشاء قاعدة البيانات:</strong><br>";
echo "افتح phpMyAdmin على <a href='http://localhost/phpmyadmin' target='_blank'>http://localhost/phpmyadmin</a><br>";
echo "أنشئ قاعدة بيانات جديدة باسم: <code>employee_management</code><br>";
echo "اختر ترميز: <code>utf8mb4_unicode_ci</code></li><br>";

echo "<li><strong>استيراد البيانات:</strong><br>";
echo "في phpMyAdmin، اختر قاعدة البيانات employee_management<br>";
echo "انقر على تبويب 'استيراد' (Import)<br>";
echo "اختر ملف <code>database.sql</code> من مجلد المشروع</li><br>";

echo "<li><strong>أو استخدم سطر الأوامر:</strong><br>";
echo "<code>mysql -u root -p -e \"CREATE DATABASE employee_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\"</code><br>";
echo "<code>mysql -u root -p employee_management < database.sql</code></li>";
echo "</ol>";

echo "<hr>";
echo "<p><strong>بعد إنشاء قاعدة البيانات، احذف هذا الملف وجرب الوصول للنظام مرة أخرى.</strong></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}
h2, h3 {
    color: #333;
}
code {
    background-color: #f8f8f8;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}
hr {
    border: none;
    border-top: 1px solid #ddd;
    margin: 20px 0;
}
</style>
