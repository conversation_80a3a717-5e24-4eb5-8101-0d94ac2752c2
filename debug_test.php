<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Debug Test</h1>";

// Test 1: Basic PHP
echo "<h2>Test 1: Basic PHP</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Current time: " . date('Y-m-d H:i:s') . "<br>";

// Test 2: Database connection
echo "<h2>Test 2: Database Connection</h2>";
try {
    $host = 'localhost';
    $dbname = 'employee_management';
    $username = 'root';
    $password = '';
    
    $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "✅ Database connection successful<br>";
    
    // Test if database exists
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll();
    echo "Tables found: " . count($tables) . "<br>";
    
    foreach ($tables as $table) {
        echo "- " . $table['Tables_in_employee_management'] . "<br>";
    }
    
} catch (PDOException $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "<br>";
}

// Test 3: Include files
echo "<h2>Test 3: Include Files</h2>";
try {
    if (file_exists('config/database.php')) {
        echo "✅ config/database.php exists<br>";
        include_once 'config/database.php';
        echo "✅ config/database.php included successfully<br>";
    } else {
        echo "❌ config/database.php not found<br>";
    }
    
    if (file_exists('includes/functions.php')) {
        echo "✅ includes/functions.php exists<br>";
        include_once 'includes/functions.php';
        echo "✅ includes/functions.php included successfully<br>";
    } else {
        echo "❌ includes/functions.php not found<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Include error: " . $e->getMessage() . "<br>";
}

// Test 4: Session
echo "<h2>Test 4: Session</h2>";
try {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    echo "✅ Session started successfully<br>";
    echo "Session ID: " . session_id() . "<br>";
} catch (Exception $e) {
    echo "❌ Session error: " . $e->getMessage() . "<br>";
}

echo "<h2>Test Complete</h2>";
echo "If you see this message, PHP is working correctly.";
?>
