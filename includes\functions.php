<?php
/**
 * الدوال المساعدة للنظام
 * System Helper Functions
 */

require_once __DIR__ . '/../config/database.php';

/**
 * بدء الجلسة
 */
function startSession() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
}

/**
 * التحقق من تسجيل الدخول
 */
function isLoggedIn() {
    startSession();
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * التحقق من الصلاحيات
 */
function hasPermission($required_role) {
    startSession();
    if (!isLoggedIn()) return false;
    
    $user_role = $_SESSION['user_role'];
    $roles_hierarchy = ['viewer' => 1, 'hr_employee' => 2, 'hr_manager' => 3, 'admin' => 4];
    
    return $roles_hierarchy[$user_role] >= $roles_hierarchy[$required_role];
}

/**
 * إعادة توجيه إذا لم يكن مسجل دخول
 */
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: login.php');
        exit();
    }
}

/**
 * إعادة توجيه إذا لم تكن لديه صلاحية
 */
function requirePermission($required_role) {
    requireLogin();
    if (!hasPermission($required_role)) {
        header('Location: index.php?error=no_permission');
        exit();
    }
}

/**
 * تنظيف البيانات المدخلة
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * التحقق من صحة الرقم القومي المصري
 */
function validateNationalId($national_id) {
    // التحقق من الطول (14 رقم)
    if (strlen($national_id) != 14 || !is_numeric($national_id)) {
        return false;
    }

    // التحقق من القرن والسنة
    $century = substr($national_id, 0, 1);
    if (!in_array($century, ['2', '3'])) {
        return false;
    }

    // التحقق من الشهر
    $month = substr($national_id, 3, 2);
    if ($month < 1 || $month > 12) {
        return false;
    }

    // التحقق من اليوم
    $day = substr($national_id, 5, 2);
    if ($day < 1 || $day > 31) {
        return false;
    }

    // التحقق من كود المحافظة
    $governorate_code = substr($national_id, 7, 2);
    if ($governorate_code < 1 || $governorate_code > 35) {
        return false;
    }

    return true;
}

/**
 * استخراج تاريخ الميلاد من الرقم القومي المصري
 */
function extractBirthDateFromNationalId($national_id) {
    if (!validateNationalId($national_id)) {
        return null;
    }

    // استخراج أجزاء التاريخ
    $century_code = substr($national_id, 0, 1);
    $year = substr($national_id, 1, 2);
    $month = substr($national_id, 3, 2);
    $day = substr($national_id, 5, 2);

    // تحديد القرن
    $full_year = ($century_code == '2') ? '19' . $year : '20' . $year;

    // تكوين التاريخ
    $birth_date = $full_year . '-' . $month . '-' . $day;

    // التحقق من صحة التاريخ
    if (checkdate($month, $day, $full_year)) {
        return $birth_date;
    }

    return null;
}

/**
 * استخراج النوع من الرقم القومي المصري
 */
function extractGenderFromNationalId($national_id) {
    if (!validateNationalId($national_id)) {
        return null;
    }

    // الرقم قبل الأخير يحدد النوع
    $gender_digit = substr($national_id, 12, 1);

    // إذا كان زوجي = أنثى، إذا كان فردي = ذكر
    return ($gender_digit % 2 == 0) ? 'أنثى' : 'ذكر';
}

/**
 * استخراج محافظة الميلاد من الرقم القومي المصري
 */
function extractBirthGovernorateFromNationalId($national_id) {
    if (!validateNationalId($national_id)) {
        return null;
    }

    $governorate_code = (int)substr($national_id, 7, 2);

    $governorate_codes = [
        1 => 'القاهرة',
        2 => 'الإسكندرية',
        3 => 'بورسعيد',
        4 => 'السويس',
        11 => 'دمياط',
        12 => 'الدقهلية',
        13 => 'الشرقية',
        14 => 'القليوبية',
        15 => 'كفر الشيخ',
        16 => 'الغربية',
        17 => 'المنوفية',
        18 => 'البحيرة',
        19 => 'الإسماعيلية',
        21 => 'الجيزة',
        22 => 'بني سويف',
        23 => 'الفيوم',
        24 => 'المنيا',
        25 => 'أسيوط',
        26 => 'سوهاج',
        27 => 'قنا',
        28 => 'أسوان',
        29 => 'الأقصر',
        31 => 'البحر الأحمر',
        32 => 'الوادي الجديد',
        33 => 'مطروح',
        34 => 'شمال سيناء',
        35 => 'جنوب سيناء'
    ];

    return isset($governorate_codes[$governorate_code]) ? $governorate_codes[$governorate_code] : 'غير محدد';
}

/**
 * استخراج الديانة من الرقم القومي المصري (تقديري)
 */
function extractReligionFromNationalId($national_id) {
    if (!validateNationalId($national_id)) {
        return null;
    }

    // هذا تقدير تقريبي بناءً على الأرقام المتسلسلة
    // الأرقام من 01-199 عادة للمسلمين
    // الأرقام من 200-299 عادة للمسيحيين
    // الأرقام من 300-399 عادة لليهود
    // لكن هذا ليس دقيق 100% ويجب استخدامه بحذر

    $sequence = (int)substr($national_id, 9, 3);

    if ($sequence >= 1 && $sequence <= 199) {
        return 'مسلم';
    } elseif ($sequence >= 200 && $sequence <= 299) {
        return 'مسيحي';
    } elseif ($sequence >= 300 && $sequence <= 399) {
        return 'يهودي';
    } else {
        return 'غير محدد';
    }
}

/**
 * استخراج جميع البيانات من الرقم القومي المصري
 */
function extractAllDataFromNationalId($national_id) {
    if (!validateNationalId($national_id)) {
        return null;
    }

    return [
        'birth_date' => extractBirthDateFromNationalId($national_id),
        'gender' => extractGenderFromNationalId($national_id),
        'birth_governorate' => extractBirthGovernorateFromNationalId($national_id),
        'religion' => extractReligionFromNationalId($national_id),
        'age' => calculateAge(extractBirthDateFromNationalId($national_id))
    ];
}

/**
 * حساب العمر من تاريخ الميلاد
 */
function calculateAge($birth_date) {
    if (!$birth_date) {
        return null;
    }

    $birth = new DateTime($birth_date);
    $today = new DateTime();
    $age = $today->diff($birth);

    return $age->y;
}

/**
 * التحقق من صحة رقم الهاتف المصري
 */
function validatePhone($phone) {
    // إزالة المسافات والرموز
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    // التحقق من الطول والبداية
    if (strlen($phone) == 11 && substr($phone, 0, 2) == '01') {
        return true;
    }
    
    // رقم دولي
    if (strlen($phone) == 13 && substr($phone, 0, 4) == '2001') {
        return true;
    }
    
    return false;
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * توليد كود موظف تلقائي
 */
function generateEmployeeCode() {
    $db = getDatabase();
    
    // الحصول على آخر كود
    $last_code = $db->fetch("SELECT employee_code FROM employees ORDER BY id DESC LIMIT 1");
    
    if ($last_code) {
        $number = intval(substr($last_code['employee_code'], 3)) + 1;
    } else {
        $number = 1;
    }
    
    return 'EMP' . str_pad($number, 3, '0', STR_PAD_LEFT);
}

/**
 * تسجيل عملية في سجل التدقيق
 */
function logAudit($employee_id, $action, $field_name = null, $old_value = null, $new_value = null) {
    startSession();
    if (!isLoggedIn()) return;
    
    $db = getDatabase();
    
    $data = [
        'employee_id' => $employee_id,
        'user_id' => $_SESSION['user_id'],
        'action' => $action,
        'field_name' => $field_name,
        'old_value' => $old_value,
        'new_value' => $new_value
    ];
    
    $db->insert('audit_log', $data);
}

/**
 * تنسيق التاريخ للعرض
 */
function formatDate($date, $format = 'Y-m-d') {
    if (empty($date)) return '';
    return date($format, strtotime($date));
}

/**
 * تنسيق التاريخ والوقت للعرض
 */
function formatDateTime($datetime, $format = 'Y-m-d H:i:s') {
    if (empty($datetime)) return '';
    return date($format, strtotime($datetime));
}

/**
 * الحصول على قائمة المحافظات المصرية
 */
function getEgyptianGovernorates() {
    return [
        'القاهرة', 'الجيزة', 'الإسكندرية', 'الدقهلية', 'البحر الأحمر', 'البحيرة',
        'الفيوم', 'الغربية', 'الإسماعيلية', 'المنوفية', 'المنيا', 'القليوبية',
        'الوادي الجديد', 'السويس', 'أسوان', 'أسيوط', 'بني سويف', 'بورسعيد',
        'دمياط', 'الشرقية', 'جنوب سيناء', 'كفر الشيخ', 'مطروح', 'الأقصر',
        'قنا', 'شمال سيناء', 'سوهاج'
    ];
}

/**
 * الحصول على قائمة الأحياء حسب المحافظة
 */
function getDistrictsByGovernorate() {
    return [
        'القاهرة' => [
            'مدينة نصر', 'المعادي', 'الزمالك', 'وسط البلد', 'مصر الجديدة', 'الدقي',
            'المهندسين', 'الزيتون', 'عين شمس', 'المطرية', 'شبرا', 'روض الفرج',
            'بولاق', 'السيدة زينب', 'مصر القديمة', 'المقطم', 'التجمع الخامس',
            'الرحاب', 'القطامية', 'المعصرة', 'طره', 'حلوان', 'المقطم', 'البساتين'
        ],
        'الجيزة' => [
            'الدقي', 'المهندسين', 'الزمالك', 'العجوزة', 'المنيل', 'الجيزة',
            'فيصل', 'الهرم', 'العمرانية', 'الوراق', 'إمبابة', 'كيت كات',
            'الشيخ زايد', '6 أكتوبر', 'الحوامدية', 'البدرشين', 'أطفيح', 'الصف'
        ],
        'الإسكندرية' => [
            'المنتزه', 'الرمل', 'سيدي جابر', 'باب شرق', 'محطة الرمل', 'كليوباترا',
            'ستانلي', 'سان ستيفانو', 'سموحة', 'فلمنج', 'الشاطبي', 'كامب شيزار',
            'الإبراهيمية', 'كرموز', 'الدخيلة', 'العجمي', 'الهانوفيل', 'برج العرب'
        ],
        'الدقهلية' => [
            'المنصورة', 'طلخا', 'ميت غمر', 'دكرنس', 'أجا', 'منية النصر',
            'السنبلاوين', 'تمي الأمديد', 'شربين', 'بلقاس', 'المطرية', 'نبروه',
            'الجمالية', 'المنزلة', 'ميت سلسيل'
        ],
        'الغربية' => [
            'طنطا', 'المحلة الكبرى', 'كفر الزيات', 'زفتى', 'السنطة', 'قطور',
            'بسيون', 'سمنود', 'الشهداء'
        ],
        'القليوبية' => [
            'بنها', 'شبرا الخيمة', 'القناطر الخيرية', 'كفر شكر', 'قليوب',
            'طوخ', 'الخانكة', 'شبين القناطر', 'الخصوص'
        ],
        'الشرقية' => [
            'الزقازيق', 'بلبيس', 'مشتول السوق', 'فاقوس', 'كفر صقر',
            'أبو حماد', 'القرين', 'ديرب نجم', 'الحسينية', 'أبو كبير',
            'منيا القمح', 'الصالحية الجديدة'
        ],
        'المنوفية' => [
            'شبين الكوم', 'منوف', 'سرس الليان', 'أشمون', 'الباجور',
            'بركة السبع', 'تلا', 'الشهداء', 'قويسنا'
        ],
        'البحيرة' => [
            'دمنهور', 'كفر الدوار', 'رشيد', 'إدكو', 'أبو المطامير',
            'الدلنجات', 'المحمودية', 'كوم حمادة', 'بدر', 'وادي النطرون'
        ],
        'الفيوم' => [
            'الفيوم', 'سنورس', 'طامية', 'إطسا', 'يوسف الصديق', 'أبشواي'
        ],
        'بني سويف' => [
            'بني سويف', 'الواسطى', 'ناصر', 'إهناسيا', 'ببا', 'الفشن', 'سمسطا'
        ],
        'المنيا' => [
            'المنيا', 'ملوي', 'سمالوط', 'المنيا الجديدة', 'العدوة', 'مغاغة',
            'بني مزار', 'مطاي', 'دير مواس', 'أبو قرقاص'
        ],
        'أسيوط' => [
            'أسيوط', 'ديروط', 'القوصية', 'منفلوط', 'أبنوب', 'الفتح',
            'ساحل سليم', 'البداري', 'صدفا', 'الغنايم', 'أبو تيج'
        ],
        'سوهاج' => [
            'سوهاج', 'أخميم', 'البلينا', 'المراغة', 'المنشأة', 'دار السلام',
            'جرجا', 'العسيرات', 'ساقلتة', 'طما', 'طهطا'
        ],
        'قنا' => [
            'قنا', 'قوص', 'نقادة', 'فرشوط', 'قفط', 'الوقف', 'دشنا', 'أبو تشت', 'نجع حمادي'
        ],
        'الأقصر' => [
            'الأقصر', 'إسنا', 'الطود', 'أرمنت', 'القرنة', 'البياضية'
        ],
        'أسوان' => [
            'أسوان', 'دراو', 'كوم أمبو', 'إدفو', 'نصر النوبة', 'كلابشة', 'أبو سمبل'
        ],
        'البحر الأحمر' => [
            'الغردقة', 'سفاجا', 'القصير', 'مرسى علم', 'الشلاتين', 'حلايب', 'رأس غارب'
        ],
        'الوادي الجديد' => [
            'الخارجة', 'الداخلة', 'الفرافرة', 'باريس', 'بلاط'
        ],
        'مطروح' => [
            'مرسى مطروح', 'الحمام', 'العلمين', 'الضبعة', 'النجيلة', 'سيوة', 'السلوم'
        ],
        'شمال سيناء' => [
            'العريش', 'الشيخ زويد', 'نخل', 'بئر العبد', 'رفح', 'الحسنة'
        ],
        'جنوب سيناء' => [
            'الطور', 'شرم الشيخ', 'دهب', 'نويبع', 'طابا', 'سانت كاترين', 'أبو رديس', 'رأس سدر'
        ],
        'بورسعيد' => [
            'بورسعيد', 'بور فؤاد', 'العرب', 'الزهور', 'الشرق', 'الضواحي', 'المناخ'
        ],
        'الإسماعيلية' => [
            'الإسماعيلية', 'فايد', 'القنطرة شرق', 'القنطرة غرب', 'أبو صوير', 'التل الكبير', 'القصاصين'
        ],
        'السويس' => [
            'السويس', 'الأربعين', 'عتاقة', 'فيصل', 'الجناين', 'السلام'
        ],
        'دمياط' => [
            'دمياط', 'رأس البر', 'فارسكور', 'الزرقا', 'كفر سعد', 'عزبة البرج'
        ],
        'كفر الشيخ' => [
            'كفر الشيخ', 'دسوق', 'فوه', 'مطوبس', 'بيلا', 'الحامول', 'بلطيم', 'الرياض', 'سيدي سالم', 'قلين'
        ]
    ];
}

/**
 * الحصول على قائمة المؤهلات
 */
function getQualifications() {
    return [
        'ابتدائية', 'إعدادية', 'ثانوية عامة', 'ثانوية فنية', 'دبلوم فني',
        'دبلوم تجاري', 'دبلوم صناعي', 'دبلوم زراعي', 'بكالوريوس',
        'ليسانس', 'دبلوم عالي', 'ماجستير', 'دكتوراه'
    ];
}

/**
 * رفع ملف
 */
function uploadFile($file, $allowed_types = ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx']) {
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'message' => 'خطأ في رفع الملف'];
    }
    
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    if (!in_array($file_extension, $allowed_types)) {
        return ['success' => false, 'message' => 'نوع الملف غير مسموح'];
    }
    
    if ($file['size'] > MAX_FILE_SIZE) {
        return ['success' => false, 'message' => 'حجم الملف كبير جداً'];
    }
    
    $filename = uniqid() . '.' . $file_extension;
    $upload_path = UPLOAD_PATH . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
        return ['success' => true, 'filename' => $filename, 'path' => $upload_path];
    }
    
    return ['success' => false, 'message' => 'فشل في حفظ الملف'];
}

/**
 * عرض رسالة تنبيه
 */
function showAlert($message, $type = 'info') {
    $class = '';
    switch ($type) {
        case 'success': $class = 'alert-success'; break;
        case 'error': $class = 'alert-danger'; break;
        case 'warning': $class = 'alert-warning'; break;
        default: $class = 'alert-info';
    }
    
    return "<div class='alert {$class} alert-dismissible fade show' role='alert'>
                {$message}
                <button type='button' class='btn-close' data-bs-dismiss='alert'></button>
            </div>";
}
?>
