<?php
/**
 * الدوال المساعدة للنظام
 * System Helper Functions
 */

require_once __DIR__ . '/../config/database.php';

/**
 * بدء الجلسة
 */
function startSession() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
}

/**
 * التحقق من تسجيل الدخول
 */
function isLoggedIn() {
    startSession();
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * التحقق من الصلاحيات
 */
function hasPermission($required_role) {
    startSession();
    if (!isLoggedIn()) return false;
    
    $user_role = $_SESSION['user_role'];
    $roles_hierarchy = ['viewer' => 1, 'hr_employee' => 2, 'hr_manager' => 3, 'admin' => 4];
    
    return $roles_hierarchy[$user_role] >= $roles_hierarchy[$required_role];
}

/**
 * إعادة توجيه إذا لم يكن مسجل دخول
 */
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: login.php');
        exit();
    }
}

/**
 * إعادة توجيه إذا لم تكن لديه صلاحية
 */
function requirePermission($required_role) {
    requireLogin();
    if (!hasPermission($required_role)) {
        header('Location: index.php?error=no_permission');
        exit();
    }
}

/**
 * تنظيف البيانات المدخلة
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * التحقق من صحة الرقم القومي المصري
 */
function validateNationalId($national_id) {
    // التحقق من الطول (14 رقم)
    if (strlen($national_id) != 14 || !is_numeric($national_id)) {
        return false;
    }
    
    // التحقق من القرن والسنة
    $century = substr($national_id, 0, 1);
    if (!in_array($century, ['2', '3'])) {
        return false;
    }
    
    // التحقق من الشهر
    $month = substr($national_id, 3, 2);
    if ($month < 1 || $month > 12) {
        return false;
    }
    
    // التحقق من اليوم
    $day = substr($national_id, 5, 2);
    if ($day < 1 || $day > 31) {
        return false;
    }
    
    // التحقق من كود المحافظة
    $governorate_code = substr($national_id, 7, 2);
    if ($governorate_code < 1 || $governorate_code > 35) {
        return false;
    }
    
    return true;
}

/**
 * التحقق من صحة رقم الهاتف المصري
 */
function validatePhone($phone) {
    // إزالة المسافات والرموز
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    // التحقق من الطول والبداية
    if (strlen($phone) == 11 && substr($phone, 0, 2) == '01') {
        return true;
    }
    
    // رقم دولي
    if (strlen($phone) == 13 && substr($phone, 0, 4) == '2001') {
        return true;
    }
    
    return false;
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * توليد كود موظف تلقائي
 */
function generateEmployeeCode() {
    $db = getDatabase();
    
    // الحصول على آخر كود
    $last_code = $db->fetch("SELECT employee_code FROM employees ORDER BY id DESC LIMIT 1");
    
    if ($last_code) {
        $number = intval(substr($last_code['employee_code'], 3)) + 1;
    } else {
        $number = 1;
    }
    
    return 'EMP' . str_pad($number, 3, '0', STR_PAD_LEFT);
}

/**
 * تسجيل عملية في سجل التدقيق
 */
function logAudit($employee_id, $action, $field_name = null, $old_value = null, $new_value = null) {
    startSession();
    if (!isLoggedIn()) return;
    
    $db = getDatabase();
    
    $data = [
        'employee_id' => $employee_id,
        'user_id' => $_SESSION['user_id'],
        'action' => $action,
        'field_name' => $field_name,
        'old_value' => $old_value,
        'new_value' => $new_value
    ];
    
    $db->insert('audit_log', $data);
}

/**
 * تنسيق التاريخ للعرض
 */
function formatDate($date, $format = 'Y-m-d') {
    if (empty($date)) return '';
    return date($format, strtotime($date));
}

/**
 * تنسيق التاريخ والوقت للعرض
 */
function formatDateTime($datetime, $format = 'Y-m-d H:i:s') {
    if (empty($datetime)) return '';
    return date($format, strtotime($datetime));
}

/**
 * الحصول على قائمة المحافظات المصرية
 */
function getEgyptianGovernorates() {
    return [
        'القاهرة', 'الجيزة', 'الإسكندرية', 'الدقهلية', 'البحر الأحمر', 'البحيرة',
        'الفيوم', 'الغربية', 'الإسماعيلية', 'المنوفية', 'المنيا', 'القليوبية',
        'الوادي الجديد', 'السويس', 'أسوان', 'أسيوط', 'بني سويف', 'بورسعيد',
        'دمياط', 'الشرقية', 'جنوب سيناء', 'كفر الشيخ', 'مطروح', 'الأقصر',
        'قنا', 'شمال سيناء', 'سوهاج'
    ];
}

/**
 * الحصول على قائمة المؤهلات
 */
function getQualifications() {
    return [
        'ابتدائية', 'إعدادية', 'ثانوية عامة', 'ثانوية فنية', 'دبلوم فني',
        'دبلوم تجاري', 'دبلوم صناعي', 'دبلوم زراعي', 'بكالوريوس',
        'ليسانس', 'دبلوم عالي', 'ماجستير', 'دكتوراه'
    ];
}

/**
 * رفع ملف
 */
function uploadFile($file, $allowed_types = ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx']) {
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'message' => 'خطأ في رفع الملف'];
    }
    
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    if (!in_array($file_extension, $allowed_types)) {
        return ['success' => false, 'message' => 'نوع الملف غير مسموح'];
    }
    
    if ($file['size'] > MAX_FILE_SIZE) {
        return ['success' => false, 'message' => 'حجم الملف كبير جداً'];
    }
    
    $filename = uniqid() . '.' . $file_extension;
    $upload_path = UPLOAD_PATH . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
        return ['success' => true, 'filename' => $filename, 'path' => $upload_path];
    }
    
    return ['success' => false, 'message' => 'فشل في حفظ الملف'];
}

/**
 * عرض رسالة تنبيه
 */
function showAlert($message, $type = 'info') {
    $class = '';
    switch ($type) {
        case 'success': $class = 'alert-success'; break;
        case 'error': $class = 'alert-danger'; break;
        case 'warning': $class = 'alert-warning'; break;
        default: $class = 'alert-info';
    }
    
    return "<div class='alert {$class} alert-dismissible fade show' role='alert'>
                {$message}
                <button type='button' class='btn-close' data-bs-dismiss='alert'></button>
            </div>";
}
?>
