        </main>
    </div>

    <!-- Footer -->
    <footer class="bg-light text-center text-muted py-3 mt-5">
        <div class="container">
            <p class="mb-0">
                &copy; <?php echo date('Y'); ?> <?php echo APP_NAME; ?>. جميع الحقوق محفوظة.
            </p>
            <small>
                تم التطوير بواسطة فريق تطوير النظم
            </small>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // تأكيد الحذف
        function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
            return confirm(message);
        }
        
        // تنسيق أرقام الهاتف
        function formatPhone(input) {
            let value = input.value.replace(/\D/g, '');
            if (value.length >= 11) {
                value = value.substring(0, 11);
                value = value.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3');
            }
            input.value = value;
        }
        
        // التحقق من الرقم القومي
        function validateNationalId(input) {
            const value = input.value.replace(/\D/g, '');
            const isValid = value.length === 14;
            
            if (isValid) {
                input.classList.remove('is-invalid');
                input.classList.add('is-valid');
            } else {
                input.classList.remove('is-valid');
                input.classList.add('is-invalid');
            }
            
            return isValid;
        }
        
        // إخفاء التنبيهات تلقائياً
        $(document).ready(function() {
            setTimeout(function() {
                $('.alert').fadeOut('slow');
            }, 5000);
            
            // تفعيل tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
            
            // تنسيق الجداول
            $('.table').addClass('table-hover');
            
            // تحسين النماذج
            $('.form-control, .form-select').on('focus', function() {
                $(this).parent().addClass('focused');
            }).on('blur', function() {
                $(this).parent().removeClass('focused');
            });
        });
        
        // طباعة الصفحة
        function printPage() {
            window.print();
        }
        
        // تصدير إلى Excel
        function exportToExcel(tableId, filename = 'data') {
            const table = document.getElementById(tableId);
            const wb = XLSX.utils.table_to_book(table);
            XLSX.writeFile(wb, filename + '.xlsx');
        }
        
        // تحديث الصفحة
        function refreshPage() {
            location.reload();
        }
        
        // البحث في الجدول
        function searchTable(inputId, tableId) {
            const input = document.getElementById(inputId);
            const table = document.getElementById(tableId);
            const rows = table.getElementsByTagName('tr');
            
            input.addEventListener('keyup', function() {
                const filter = this.value.toLowerCase();
                
                for (let i = 1; i < rows.length; i++) {
                    const row = rows[i];
                    const cells = row.getElementsByTagName('td');
                    let found = false;
                    
                    for (let j = 0; j < cells.length; j++) {
                        if (cells[j].textContent.toLowerCase().includes(filter)) {
                            found = true;
                            break;
                        }
                    }
                    
                    row.style.display = found ? '' : 'none';
                }
            });
        }
        
        // تحميل البيانات بـ AJAX
        function loadData(url, containerId, showLoader = true) {
            const container = document.getElementById(containerId);
            
            if (showLoader) {
                container.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x"></i><br>جاري التحميل...</div>';
            }
            
            fetch(url)
                .then(response => response.text())
                .then(data => {
                    container.innerHTML = data;
                })
                .catch(error => {
                    container.innerHTML = '<div class="alert alert-danger">خطأ في تحميل البيانات</div>';
                    console.error('Error:', error);
                });
        }
        
        // إرسال النموذج بـ AJAX
        function submitForm(formId, successCallback = null) {
            const form = document.getElementById(formId);
            const formData = new FormData(form);
            
            fetch(form.action, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (successCallback) {
                        successCallback(data);
                    } else {
                        location.reload();
                    }
                } else {
                    alert(data.message || 'حدث خطأ');
                }
            })
            .catch(error => {
                alert('خطأ في الاتصال');
                console.error('Error:', error);
            });
        }
        
        // تحديد الكل
        function toggleSelectAll(masterCheckbox, childClass) {
            const checkboxes = document.querySelectorAll('.' + childClass);
            checkboxes.forEach(checkbox => {
                checkbox.checked = masterCheckbox.checked;
            });
        }
        
        // عد المحدد
        function countSelected(childClass) {
            const checkboxes = document.querySelectorAll('.' + childClass + ':checked');
            return checkboxes.length;
        }
    </script>
    
    <!-- Additional scripts can be added here -->
    <?php if (isset($additional_scripts)): ?>
        <?php echo $additional_scripts; ?>
    <?php endif; ?>
    
</body>
</html>
