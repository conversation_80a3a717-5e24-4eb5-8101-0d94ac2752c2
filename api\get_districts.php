<?php
/**
 * API لجلب الأحياء حسب المحافظة
 * API to get districts by governorate
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../includes/functions.php';

// التحقق من وجود معامل المحافظة
if (!isset($_GET['governorate']) || empty($_GET['governorate'])) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'معامل المحافظة مطلوب',
        'data' => []
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$governorate = sanitizeInput($_GET['governorate']);
$districts_data = getDistrictsByGovernorate();

// التحقق من وجود المحافظة في البيانات
if (!isset($districts_data[$governorate])) {
    http_response_code(404);
    echo json_encode([
        'success' => false,
        'message' => 'المحافظة غير موجودة',
        'data' => []
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// إرجاع الأحياء
echo json_encode([
    'success' => true,
    'message' => 'تم جلب الأحياء بنجاح',
    'governorate' => $governorate,
    'data' => $districts_data[$governorate]
], JSON_UNESCAPED_UNICODE);
?>
