<?php
/**
 * صفحة تحذيرات انتهاء شهادات التجنيد/الخدمة العامة
 * Service Certificate Expiry Alerts Page
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
requireLogin();

$page_title = 'تحذيرات شهادات التجنيد/الخدمة العامة';
$db = getDatabase();

// إحصائيات تحذيرات شهادات التجنيد/الخدمة العامة
$expired_certificates = $db->fetchAll("
    SELECT *, 
           DATEDIFF(service_certificate_expiry_date, CURDATE()) as days_until_expiry,
           CASE 
               WHEN gender = 'ذكر' THEN 'شهادة التجنيد'
               WHEN gender = 'أنثى' THEN 'شهادة الخدمة العامة'
               ELSE 'شهادة الخدمة'
           END as certificate_type
    FROM employee_full_view 
    WHERE service_certificate_expiry_date IS NOT NULL 
    AND service_certificate_expiry_date < CURDATE()
    ORDER BY service_certificate_expiry_date ASC
");

$expiring_soon_certificates = $db->fetchAll("
    SELECT *, 
           DATEDIFF(service_certificate_expiry_date, CURDATE()) as days_until_expiry,
           CASE 
               WHEN gender = 'ذكر' THEN 'شهادة التجنيد'
               WHEN gender = 'أنثى' THEN 'شهادة الخدمة العامة'
               ELSE 'شهادة الخدمة'
           END as certificate_type
    FROM employee_full_view 
    WHERE service_certificate_expiry_date IS NOT NULL 
    AND service_certificate_expiry_date >= CURDATE()
    AND DATEDIFF(service_certificate_expiry_date, CURDATE()) <= 30
    ORDER BY service_certificate_expiry_date ASC
");

$early_warning_certificates = $db->fetchAll("
    SELECT *, 
           DATEDIFF(service_certificate_expiry_date, CURDATE()) as days_until_expiry,
           CASE 
               WHEN gender = 'ذكر' THEN 'شهادة التجنيد'
               WHEN gender = 'أنثى' THEN 'شهادة الخدمة العامة'
               ELSE 'شهادة الخدمة'
           END as certificate_type
    FROM employee_full_view 
    WHERE service_certificate_expiry_date IS NOT NULL 
    AND DATEDIFF(service_certificate_expiry_date, CURDATE()) BETWEEN 31 AND 90
    ORDER BY service_certificate_expiry_date ASC
");

// إحصائيات عامة
$total_with_certificates = $db->fetchColumn("
    SELECT COUNT(*) FROM employees 
    WHERE service_certificate_expiry_date IS NOT NULL
");

$total_expired = count($expired_certificates);
$total_expiring_soon = count($expiring_soon_certificates);
$total_early_warning = count($early_warning_certificates);

include '../includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-certificate me-2 text-warning"></i>
        تحذيرات شهادات التجنيد/الخدمة العامة
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.print()">
                <i class="fas fa-print"></i> طباعة
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportToExcel()">
                <i class="fas fa-file-excel"></i> تصدير Excel
            </button>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-danger">
            <div class="card-body text-center">
                <div class="text-danger">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <h4 class="text-danger"><?php echo $total_expired; ?></h4>
                    <p class="card-text">شهادات منتهية الصلاحية</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-warning">
            <div class="card-body text-center">
                <div class="text-warning">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h4 class="text-warning"><?php echo $total_expiring_soon; ?></h4>
                    <p class="card-text">تنتهي خلال 30 يوم</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-info">
            <div class="card-body text-center">
                <div class="text-info">
                    <i class="fas fa-bell fa-2x mb-2"></i>
                    <h4 class="text-info"><?php echo $total_early_warning; ?></h4>
                    <p class="card-text">تحذير مبكر (31-90 يوم)</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-success">
            <div class="card-body text-center">
                <div class="text-success">
                    <i class="fas fa-certificate fa-2x mb-2"></i>
                    <h4 class="text-success"><?php echo $total_with_certificates; ?></h4>
                    <p class="card-text">إجمالي الشهادات</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الشهادات منتهية الصلاحية -->
<?php if (!empty($expired_certificates)): ?>
<div class="card mb-4">
    <div class="card-header bg-danger text-white">
        <h5 class="card-title mb-0">
            <i class="fas fa-exclamation-triangle me-2"></i>
            شهادات منتهية الصلاحية (<?php echo count($expired_certificates); ?>)
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>كود الموظف</th>
                        <th>اسم الموظف</th>
                        <th>النوع</th>
                        <th>نوع الشهادة</th>
                        <th>تاريخ الانتهاء</th>
                        <th>منتهية منذ</th>
                        <th>العمليات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($expired_certificates as $employee): ?>
                    <tr>
                        <td>
                            <span class="badge bg-primary"><?php echo $employee['employee_code']; ?></span>
                        </td>
                        <td>
                            <strong><?php echo $employee['full_name']; ?></strong>
                        </td>
                        <td>
                            <span class="badge <?php echo $employee['gender'] == 'ذكر' ? 'bg-info' : 'bg-pink'; ?>">
                                <?php echo $employee['gender']; ?>
                            </span>
                        </td>
                        <td><?php echo $employee['certificate_type']; ?></td>
                        <td>
                            <span class="text-danger fw-bold">
                                <?php echo formatDate($employee['service_certificate_expiry_date']); ?>
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-danger">
                                <?php echo abs($employee['days_until_expiry']); ?> يوم
                            </span>
                        </td>
                        <td>
                            <a href="view_employee.php?id=<?php echo $employee['id']; ?>" 
                               class="btn btn-sm btn-outline-primary" 
                               data-bs-toggle="tooltip" title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </a>
                            <?php if (hasPermission('hr_employee')): ?>
                            <a href="edit_employee.php?id=<?php echo $employee['id']; ?>" 
                               class="btn btn-sm btn-outline-warning" 
                               data-bs-toggle="tooltip" title="تحديث الشهادة">
                                <i class="fas fa-edit"></i>
                            </a>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- الشهادات التي تنتهي قريباً -->
<?php if (!empty($expiring_soon_certificates)): ?>
<div class="card mb-4">
    <div class="card-header bg-warning text-dark">
        <h5 class="card-title mb-0">
            <i class="fas fa-clock me-2"></i>
            شهادات تنتهي خلال 30 يوم (<?php echo count($expiring_soon_certificates); ?>)
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>كود الموظف</th>
                        <th>اسم الموظف</th>
                        <th>النوع</th>
                        <th>نوع الشهادة</th>
                        <th>تاريخ الانتهاء</th>
                        <th>باقي</th>
                        <th>العمليات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($expiring_soon_certificates as $employee): ?>
                    <tr>
                        <td>
                            <span class="badge bg-primary"><?php echo $employee['employee_code']; ?></span>
                        </td>
                        <td>
                            <strong><?php echo $employee['full_name']; ?></strong>
                        </td>
                        <td>
                            <span class="badge <?php echo $employee['gender'] == 'ذكر' ? 'bg-info' : 'bg-pink'; ?>">
                                <?php echo $employee['gender']; ?>
                            </span>
                        </td>
                        <td><?php echo $employee['certificate_type']; ?></td>
                        <td>
                            <span class="text-warning fw-bold">
                                <?php echo formatDate($employee['service_certificate_expiry_date']); ?>
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-warning text-dark">
                                <?php echo $employee['days_until_expiry']; ?> يوم
                            </span>
                        </td>
                        <td>
                            <a href="view_employee.php?id=<?php echo $employee['id']; ?>" 
                               class="btn btn-sm btn-outline-primary" 
                               data-bs-toggle="tooltip" title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </a>
                            <?php if (hasPermission('hr_employee')): ?>
                            <a href="edit_employee.php?id=<?php echo $employee['id']; ?>" 
                               class="btn btn-sm btn-outline-warning" 
                               data-bs-toggle="tooltip" title="تحديث الشهادة">
                                <i class="fas fa-edit"></i>
                            </a>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- التحذير المبكر -->
<?php if (!empty($early_warning_certificates)): ?>
<div class="card mb-4">
    <div class="card-header bg-info text-white">
        <h5 class="card-title mb-0">
            <i class="fas fa-bell me-2"></i>
            تحذير مبكر - تنتهي خلال 31-90 يوم (<?php echo count($early_warning_certificates); ?>)
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>كود الموظف</th>
                        <th>اسم الموظف</th>
                        <th>النوع</th>
                        <th>نوع الشهادة</th>
                        <th>تاريخ الانتهاء</th>
                        <th>باقي</th>
                        <th>العمليات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($early_warning_certificates as $employee): ?>
                    <tr>
                        <td>
                            <span class="badge bg-primary"><?php echo $employee['employee_code']; ?></span>
                        </td>
                        <td>
                            <strong><?php echo $employee['full_name']; ?></strong>
                        </td>
                        <td>
                            <span class="badge <?php echo $employee['gender'] == 'ذكر' ? 'bg-info' : 'bg-pink'; ?>">
                                <?php echo $employee['gender']; ?>
                            </span>
                        </td>
                        <td><?php echo $employee['certificate_type']; ?></td>
                        <td>
                            <?php echo formatDate($employee['service_certificate_expiry_date']); ?>
                        </td>
                        <td>
                            <span class="badge bg-info">
                                <?php echo $employee['days_until_expiry']; ?> يوم
                            </span>
                        </td>
                        <td>
                            <a href="view_employee.php?id=<?php echo $employee['id']; ?>" 
                               class="btn btn-sm btn-outline-primary" 
                               data-bs-toggle="tooltip" title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </a>
                            <?php if (hasPermission('hr_employee')): ?>
                            <a href="edit_employee.php?id=<?php echo $employee['id']; ?>" 
                               class="btn btn-sm btn-outline-warning" 
                               data-bs-toggle="tooltip" title="تحديث الشهادة">
                                <i class="fas fa-edit"></i>
                            </a>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- رسالة عدم وجود تحذيرات -->
<?php if (empty($expired_certificates) && empty($expiring_soon_certificates) && empty($early_warning_certificates)): ?>
<div class="card">
    <div class="card-body text-center py-5">
        <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
        <h4 class="text-success">ممتاز! لا توجد تحذيرات</h4>
        <p class="text-muted">جميع شهادات التجنيد/الخدمة العامة سارية المفعول</p>
        <a href="add_employee.php" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>إضافة موظف جديد
        </a>
    </div>
</div>
<?php endif; ?>

<style>
.bg-pink {
    background-color: #e91e63 !important;
}

@media print {
    .btn-toolbar, .card-header .btn {
        display: none !important;
    }
}
</style>

<script>
// تفعيل tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// تصدير إلى Excel
function exportToExcel() {
    // يمكن تطوير هذه الوظيفة لاحقاً
    alert('سيتم تطوير وظيفة التصدير قريباً');
}
</script>

<?php include '../includes/footer.php'; ?>
