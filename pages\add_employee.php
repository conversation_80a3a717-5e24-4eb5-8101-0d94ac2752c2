<?php
/**
 * صفحة إضافة موظف جديد
 * Add New Employee Page
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';

// التحقق من الصلاحيات
requirePermission('hr_employee');

$page_title = 'إضافة موظف جديد';
$db = getDatabase();
$errors = [];
$success_message = '';

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // تنظيف البيانات
    $data = sanitizeInput($_POST);
    
    // التحقق من صحة البيانات
    
    // الاسم الخماسي
    if (empty($data['first_name'])) {
        $errors['first_name'] = 'الاسم الأول مطلوب';
    }
    if (empty($data['father_name'])) {
        $errors['father_name'] = 'اسم الأب مطلوب';
    }
    if (empty($data['grandfather_name'])) {
        $errors['grandfather_name'] = 'اسم الجد مطلوب';
    }
    if (empty($data['family_name'])) {
        $errors['family_name'] = 'اسم العائلة مطلوب';
    }
    
    // الرقم القومي
    if (empty($data['national_id'])) {
        $errors['national_id'] = 'الرقم القومي مطلوب';
    } elseif (!validateNationalId($data['national_id'])) {
        $errors['national_id'] = 'الرقم القومي غير صحيح';
    } else {
        // التحقق من عدم التكرار
        $existing = $db->fetchOne("SELECT id FROM employees WHERE national_id = ?", [$data['national_id']]);
        if ($existing) {
            $errors['national_id'] = 'الرقم القومي موجود بالفعل';
        }
    }
    
    // رقم التأمين
    if (!empty($data['insurance_number'])) {
        $existing = $db->fetchOne("SELECT id FROM employees WHERE insurance_number = ?", [$data['insurance_number']]);
        if ($existing) {
            $errors['insurance_number'] = 'رقم التأمين موجود بالفعل';
        }
    }
    
    // رقم الهاتف
    if (empty($data['phone'])) {
        $errors['phone'] = 'رقم الهاتف مطلوب';
    } elseif (!validatePhone($data['phone'])) {
        $errors['phone'] = 'رقم الهاتف غير صحيح';
    }
    
    // البريد الإلكتروني
    if (!empty($data['email']) && !validateEmail($data['email'])) {
        $errors['email'] = 'البريد الإلكتروني غير صحيح';
    } elseif (!empty($data['email'])) {
        $existing = $db->fetch("SELECT id FROM employees WHERE email = ?", [$data['email']]);
        if ($existing) {
            $errors['email'] = 'البريد الإلكتروني موجود بالفعل';
        }
    }
    
    // الحقول المطلوبة الأخرى
    if (empty($data['governorate'])) {
        $errors['governorate'] = 'المحافظة مطلوبة';
    }
    if (empty($data['street'])) {
        $errors['street'] = 'العنوان مطلوب';
    }
    if (empty($data['gender'])) {
        $errors['gender'] = 'النوع مطلوب';
    }
    if (empty($data['marital_status'])) {
        $errors['marital_status'] = 'الحالة الاجتماعية مطلوبة';
    }

    // التحقق من وجود مؤهل واحد على الأقل
    if (empty($data['qualifications_data'])) {
        $errors['qualifications'] = 'يجب إضافة مؤهل علمي واحد على الأقل';
    } else {
        $qualifications = json_decode($data['qualifications_data'], true);
        if (!$qualifications || !is_array($qualifications) || count($qualifications) == 0) {
            $errors['qualifications'] = 'يجب إضافة مؤهل علمي واحد على الأقل';
        }
    }

    // معالجة رفع الصورة الشخصية
    $photo_filename = null;
    if (isset($_FILES['photo']) && $_FILES['photo']['error'] == UPLOAD_ERR_OK) {
        $upload_dir = 'uploads/employee_photos';

        // التحقق من نوع الملف
        $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        $file_type = $_FILES['photo']['type'];

        if (!in_array($file_type, $allowed_types)) {
            $errors['photo'] = 'نوع الملف غير مدعوم. يرجى رفع صورة بصيغة JPG, PNG, GIF أو WEBP';
        }

        // التحقق من حجم الملف (5MB)
        if ($_FILES['photo']['size'] > 5 * 1024 * 1024) {
            $errors['photo'] = 'حجم الملف كبير جداً. الحد الأقصى 5MB';
        }

        // إذا لم توجد أخطاء، احفظ الملف
        if (!isset($errors['photo'])) {
            $file_extension = pathinfo($_FILES['photo']['name'], PATHINFO_EXTENSION);
            $photo_filename = 'employee_' . time() . '_' . rand(1000, 9999) . '.' . $file_extension;
            $upload_path = $upload_dir . '/' . $photo_filename;

            if (!move_uploaded_file($_FILES['photo']['tmp_name'], $upload_path)) {
                $errors['photo'] = 'فشل في رفع الصورة. يرجى المحاولة مرة أخرى';
                $photo_filename = null;
            }
        }
    }

    // معالجة رفع صورة الرقم القومي
    $national_id_photo_filename = null;
    if (isset($_FILES['national_id_photo']) && $_FILES['national_id_photo']['error'] == UPLOAD_ERR_OK) {
        $upload_dir = 'uploads/national_id_photos';

        // التحقق من نوع الملف
        $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'application/pdf'];
        $file_type = $_FILES['national_id_photo']['type'];

        if (!in_array($file_type, $allowed_types)) {
            $errors['national_id_photo'] = 'نوع الملف غير مدعوم. يرجى رفع صورة بصيغة JPG, PNG, GIF, WEBP أو PDF';
        }

        // التحقق من حجم الملف (10MB)
        if ($_FILES['national_id_photo']['size'] > 10 * 1024 * 1024) {
            $errors['national_id_photo'] = 'حجم الملف كبير جداً. الحد الأقصى 10MB';
        }

        // إذا لم توجد أخطاء، احفظ الملف
        if (!isset($errors['national_id_photo'])) {
            $file_extension = pathinfo($_FILES['national_id_photo']['name'], PATHINFO_EXTENSION);
            $national_id_photo_filename = 'national_id_' . time() . '_' . rand(1000, 9999) . '.' . $file_extension;
            $upload_path = $upload_dir . '/' . $national_id_photo_filename;

            if (!move_uploaded_file($_FILES['national_id_photo']['tmp_name'], $upload_path)) {
                $errors['national_id_photo'] = 'فشل في رفع صورة الرقم القومي. يرجى المحاولة مرة أخرى';
                $national_id_photo_filename = null;
            }
        }
    }
    
    // إذا لم توجد أخطاء، احفظ البيانات
    if (empty($errors)) {
        try {
            // توليد كود الموظف
            $employee_code = generateEmployeeCode();
            
            // استخراج البيانات من الرقم القومي
            $extracted_data = extractAllDataFromNationalId($data['national_id']);

            // إعداد البيانات للحفظ
            $employee_data = [
                'employee_code' => $employee_code,
                'first_name' => $data['first_name'],
                'father_name' => $data['father_name'],
                'grandfather_name' => $data['grandfather_name'],
                'family_name' => $data['family_name'],
                'nickname' => $data['nickname'] ?: null,
                'photo' => $photo_filename,
                'national_id' => $data['national_id'],
                'national_id_expiry_date' => $data['national_id_expiry_date'] ?: null,
                'national_id_photo' => $national_id_photo_filename,
                'birth_date' => $extracted_data['birth_date'] ?? null,
                'age' => $extracted_data['age'] ?? null,
                'birth_governorate' => $extracted_data['birth_governorate'] ?? null,
                'insurance_number' => $data['insurance_number'] ?: null,
                'country' => $data['country'] ?: 'مصر',
                'governorate' => $data['governorate'],
                'city' => $data['city'] ?: null,
                'street' => $data['street'],
                'postal_code' => $data['postal_code'] ?: null,
                'phone' => $data['phone'],
                'email' => $data['email'] ?: null,
                'gender' => $extracted_data['gender'] ?? $data['gender'],
                'religion' => $extracted_data['religion'] ?? $data['religion'] ?: null,
                'marital_status' => $data['marital_status'],
                'children_count' => ($data['marital_status'] == 'أعزب') ? 0 : (int)($data['children_count'] ?? 0),
                'has_disability' => isset($data['has_disability']) ? 1 : 0,
                'disability_type' => $data['disability_type'] ?: null,
                'disability_percentage' => isset($data['has_disability']) && !empty($data['disability_percentage']) ? (float)$data['disability_percentage'] : null,
                'military_status' => $data['military_status'] ?: 'غير محدد',
                'military_status_date' => $data['military_status_date'] ?: null,
                'military_notes' => $data['military_notes'] ?: null,
                'public_service_status' => $data['public_service_status'] ?: 'غير محدد',
                'public_service_date' => $data['public_service_date'] ?: null,
                'public_service_notes' => $data['public_service_notes'] ?: null,
                'created_by' => $_SESSION['user_id']
            ];
            
            // حفظ البيانات
            $employee_id = $db->insert('employees', $employee_data);

            // حفظ المؤهلات العلمية
            if ($employee_id && !empty($data['qualifications_data'])) {
                $qualifications = json_decode($data['qualifications_data'], true);
                if ($qualifications && is_array($qualifications)) {
                    foreach ($qualifications as $qual) {
                        $qualification_data = [
                            'employee_id' => $employee_id,
                            'qualification_type' => $qual['type'],
                            'qualification_date' => $qual['date'],
                            'specialization' => $qual['specialization'] ?: null,
                            'institution' => $qual['institution'] ?: null,
                            'grade' => $qual['grade'] ?: null
                        ];
                        $db->insert('employee_qualifications', $qualification_data);
                    }
                }
            }

            // تسجيل في سجل التدقيق
            logAudit($employee_id, 'INSERT');

            $success_message = 'تم إضافة الموظف بنجاح برقم: ' . $employee_code;
            
            // إعادة تعيين النموذج
            $_POST = [];
            
        } catch (Exception $e) {
            $errors['general'] = 'حدث خطأ أثناء حفظ البيانات: ' . $e->getMessage();
        }
    }
}

include '../includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-plus me-2"></i>
        إضافة موظف جديد
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="view_employees.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
        </a>
    </div>
</div>

<?php if (!empty($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo $success_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (!empty($errors['general'])): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo $errors['general']; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-user-edit me-2"></i>
            بيانات الموظف
        </h5>
    </div>
    <div class="card-body">
        <form method="POST" action="" enctype="multipart/form-data" novalidate>
            <!-- البيانات الشخصية -->
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="text-primary border-bottom pb-2 mb-3">
                        <i class="fas fa-user me-2"></i>البيانات الشخصية
                    </h6>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="first_name" class="form-label">الاسم الأول <span class="text-danger">*</span></label>
                    <input type="text" 
                           class="form-control <?php echo isset($errors['first_name']) ? 'is-invalid' : ''; ?>" 
                           id="first_name" 
                           name="first_name" 
                           value="<?php echo $_POST['first_name'] ?? ''; ?>"
                           required>
                    <?php if (isset($errors['first_name'])): ?>
                        <div class="invalid-feedback"><?php echo $errors['first_name']; ?></div>
                    <?php endif; ?>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="father_name" class="form-label">اسم الأب <span class="text-danger">*</span></label>
                    <input type="text" 
                           class="form-control <?php echo isset($errors['father_name']) ? 'is-invalid' : ''; ?>" 
                           id="father_name" 
                           name="father_name" 
                           value="<?php echo $_POST['father_name'] ?? ''; ?>"
                           required>
                    <?php if (isset($errors['father_name'])): ?>
                        <div class="invalid-feedback"><?php echo $errors['father_name']; ?></div>
                    <?php endif; ?>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="grandfather_name" class="form-label">اسم الجد <span class="text-danger">*</span></label>
                    <input type="text" 
                           class="form-control <?php echo isset($errors['grandfather_name']) ? 'is-invalid' : ''; ?>" 
                           id="grandfather_name" 
                           name="grandfather_name" 
                           value="<?php echo $_POST['grandfather_name'] ?? ''; ?>"
                           required>
                    <?php if (isset($errors['grandfather_name'])): ?>
                        <div class="invalid-feedback"><?php echo $errors['grandfather_name']; ?></div>
                    <?php endif; ?>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="family_name" class="form-label">اسم العائلة <span class="text-danger">*</span></label>
                    <input type="text" 
                           class="form-control <?php echo isset($errors['family_name']) ? 'is-invalid' : ''; ?>" 
                           id="family_name" 
                           name="family_name" 
                           value="<?php echo $_POST['family_name'] ?? ''; ?>"
                           required>
                    <?php if (isset($errors['family_name'])): ?>
                        <div class="invalid-feedback"><?php echo $errors['family_name']; ?></div>
                    <?php endif; ?>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="nickname" class="form-label">اللقب</label>
                    <input type="text"
                           class="form-control"
                           id="nickname"
                           name="nickname"
                           value="<?php echo $_POST['nickname'] ?? ''; ?>">
                </div>

                <div class="col-md-6 mb-3">
                    <label for="photo" class="form-label">الصورة الشخصية</label>
                    <input type="file"
                           class="form-control <?php echo isset($errors['photo']) ? 'is-invalid' : ''; ?>"
                           id="photo"
                           name="photo"
                           accept="image/jpeg,image/jpg,image/png,image/gif,image/webp">
                    <?php if (isset($errors['photo'])): ?>
                        <div class="invalid-feedback"><?php echo $errors['photo']; ?></div>
                    <?php endif; ?>
                    <div class="form-text">اختياري - JPG, PNG, GIF (حد أقصى 5MB)</div>

                    <!-- معاينة الصورة -->
                    <div id="photo-preview" class="mt-2" style="display: none;">
                        <img id="preview-image" src="" alt="معاينة الصورة"
                             style="max-width: 150px; max-height: 150px; border: 2px solid #dee2e6; border-radius: 8px;">
                        <button type="button" class="btn btn-sm btn-outline-danger ms-2" onclick="removePhotoPreview()">
                            <i class="fas fa-times"></i> إزالة
                        </button>
                    </div>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="national_id" class="form-label">الرقم القومي <span class="text-danger">*</span></label>
                    <input type="text"
                           class="form-control <?php echo isset($errors['national_id']) ? 'is-invalid' : ''; ?>"
                           id="national_id"
                           name="national_id"
                           value="<?php echo $_POST['national_id'] ?? ''; ?>"
                           maxlength="14"
                           pattern="[0-9]{14}"
                           onkeyup="validateAndExtractNationalId(this)"
                           required>
                    <?php if (isset($errors['national_id'])): ?>
                        <div class="invalid-feedback"><?php echo $errors['national_id']; ?></div>
                    <?php endif; ?>
                    <div class="form-text">14 رقم - سيتم استخراج البيانات تلقائياً</div>
                </div>

                <!-- الحقول المستخرجة من الرقم القومي -->
                <div class="col-md-6 mb-3">
                    <label for="birth_date" class="form-label">تاريخ الميلاد</label>
                    <input type="date"
                           class="form-control"
                           id="birth_date"
                           name="birth_date"
                           value="<?php echo $_POST['birth_date'] ?? ''; ?>"
                           readonly>
                    <div class="form-text">يتم استخراجه من الرقم القومي</div>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="age" class="form-label">العمر</label>
                    <input type="number"
                           class="form-control"
                           id="age"
                           name="age"
                           value="<?php echo $_POST['age'] ?? ''; ?>"
                           readonly>
                    <div class="form-text">يتم حسابه تلقائياً</div>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="birth_governorate" class="form-label">محافظة الميلاد</label>
                    <input type="text"
                           class="form-control"
                           id="birth_governorate"
                           name="birth_governorate"
                           value="<?php echo $_POST['birth_governorate'] ?? ''; ?>"
                           readonly>
                    <div class="form-text">يتم استخراجها من الرقم القومي</div>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="national_id_expiry_date" class="form-label">تاريخ انتهاء الرقم القومي</label>
                    <input type="date"
                           class="form-control <?php echo isset($errors['national_id_expiry_date']) ? 'is-invalid' : ''; ?>"
                           id="national_id_expiry_date"
                           name="national_id_expiry_date"
                           value="<?php echo $_POST['national_id_expiry_date'] ?? ''; ?>">
                    <?php if (isset($errors['national_id_expiry_date'])): ?>
                        <div class="invalid-feedback"><?php echo $errors['national_id_expiry_date']; ?></div>
                    <?php endif; ?>
                    <div class="form-text">تاريخ انتهاء صلاحية البطاقة</div>
                </div>

                <div class="col-md-12 mb-3">
                    <label for="national_id_photo" class="form-label">صورة الرقم القومي</label>
                    <input type="file"
                           class="form-control <?php echo isset($errors['national_id_photo']) ? 'is-invalid' : ''; ?>"
                           id="national_id_photo"
                           name="national_id_photo"
                           accept="image/jpeg,image/jpg,image/png,image/gif,image/webp,application/pdf">
                    <?php if (isset($errors['national_id_photo'])): ?>
                        <div class="invalid-feedback"><?php echo $errors['national_id_photo']; ?></div>
                    <?php endif; ?>
                    <div class="form-text">اختياري - JPG, PNG, GIF, PDF (حد أقصى 10MB)</div>

                    <!-- معاينة صورة الرقم القومي -->
                    <div id="national-id-photo-preview" class="mt-2" style="display: none;">
                        <img id="national-id-preview-image" src="" alt="معاينة صورة الرقم القومي"
                             style="max-width: 200px; max-height: 150px; border: 2px solid #dee2e6; border-radius: 8px;">
                        <button type="button" class="btn btn-sm btn-outline-danger ms-2" onclick="removeNationalIdPhotoPreview()">
                            <i class="fas fa-times"></i> إزالة
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- بيانات الاتصال والعنوان -->
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="text-primary border-bottom pb-2 mb-3">
                        <i class="fas fa-map-marker-alt me-2"></i>بيانات الاتصال والعنوان
                    </h6>
                </div>



                <div class="col-md-6 mb-3">
                    <label for="phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                    <input type="tel"
                           class="form-control <?php echo isset($errors['phone']) ? 'is-invalid' : ''; ?>"
                           id="phone"
                           name="phone"
                           value="<?php echo $_POST['phone'] ?? ''; ?>"
                           onkeyup="formatPhone(this)"
                           required>
                    <?php if (isset($errors['phone'])): ?>
                        <div class="invalid-feedback"><?php echo $errors['phone']; ?></div>
                    <?php endif; ?>
                    <div class="form-text">مثال: 01012345678</div>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="email" class="form-label">البريد الإلكتروني</label>
                    <input type="email"
                           class="form-control <?php echo isset($errors['email']) ? 'is-invalid' : ''; ?>"
                           id="email"
                           name="email"
                           value="<?php echo $_POST['email'] ?? ''; ?>">
                    <?php if (isset($errors['email'])): ?>
                        <div class="invalid-feedback"><?php echo $errors['email']; ?></div>
                    <?php endif; ?>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="country" class="form-label">البلد</label>
                    <input type="text"
                           class="form-control"
                           id="country"
                           name="country"
                           value="<?php echo $_POST['country'] ?? 'مصر'; ?>">
                </div>

                <div class="col-md-6 mb-3">
                    <label for="governorate" class="form-label">المحافظة <span class="text-danger">*</span></label>
                    <select class="form-select <?php echo isset($errors['governorate']) ? 'is-invalid' : ''; ?>"
                            id="governorate"
                            name="governorate"
                            required>
                        <option value="">اختر المحافظة</option>
                        <?php foreach (getEgyptianGovernorates() as $gov): ?>
                            <option value="<?php echo $gov; ?>" <?php echo (($_POST['governorate'] ?? '') == $gov) ? 'selected' : ''; ?>>
                                <?php echo $gov; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <?php if (isset($errors['governorate'])): ?>
                        <div class="invalid-feedback"><?php echo $errors['governorate']; ?></div>
                    <?php endif; ?>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="street" class="form-label">العنوان <span class="text-danger">*</span></label>
                    <input type="text"
                           class="form-control <?php echo isset($errors['street']) ? 'is-invalid' : ''; ?>"
                           id="street"
                           name="street"
                           value="<?php echo $_POST['street'] ?? ''; ?>"
                           required>
                    <?php if (isset($errors['street'])): ?>
                        <div class="invalid-feedback"><?php echo $errors['street']; ?></div>
                    <?php endif; ?>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="postal_code" class="form-label">الرمز البريدي</label>
                    <input type="text"
                           class="form-control"
                           id="postal_code"
                           name="postal_code"
                           value="<?php echo $_POST['postal_code'] ?? ''; ?>">
                </div>

                <div class="col-md-6 mb-3">
                    <label for="city" class="form-label">الحي/المدينة</label>
                    <div class="input-group">
                        <select class="form-select"
                                id="city"
                                name="city">
                            <option value="">اختر الحي/المدينة</option>
                            <!-- سيتم ملء الخيارات بواسطة JavaScript -->
                        </select>
                        <button class="btn btn-outline-secondary"
                                type="button"
                                id="addCustomCity"
                                data-bs-toggle="tooltip"
                                title="إضافة حي جديد">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                    <div class="form-text">اختر من القائمة أو أضف حي جديد</div>
                </div>
            </div>

            <!-- البيانات الشخصية والاجتماعية -->
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="text-primary border-bottom pb-2 mb-3">
                        <i class="fas fa-user-friends me-2"></i>البيانات الشخصية والاجتماعية
                    </h6>
                </div>

                <div class="col-md-4 mb-3">
                    <label for="gender" class="form-label">النوع <span class="text-danger">*</span></label>
                    <select class="form-select <?php echo isset($errors['gender']) ? 'is-invalid' : ''; ?>"
                            id="gender"
                            name="gender"
                            required>
                        <option value="">اختر النوع</option>
                        <option value="ذكر" <?php echo (($_POST['gender'] ?? '') == 'ذكر') ? 'selected' : ''; ?>>ذكر</option>
                        <option value="أنثى" <?php echo (($_POST['gender'] ?? '') == 'أنثى') ? 'selected' : ''; ?>>أنثى</option>
                    </select>
                    <?php if (isset($errors['gender'])): ?>
                        <div class="invalid-feedback"><?php echo $errors['gender']; ?></div>
                    <?php endif; ?>
                </div>

                <div class="col-md-4 mb-3">
                    <label for="religion" class="form-label">الديانة</label>
                    <select class="form-select" id="religion" name="religion">
                        <option value="">اختر الديانة</option>
                        <option value="مسلم" <?php echo (($_POST['religion'] ?? '') == 'مسلم') ? 'selected' : ''; ?>>مسلم</option>
                        <option value="مسيحي" <?php echo (($_POST['religion'] ?? '') == 'مسيحي') ? 'selected' : ''; ?>>مسيحي</option>
                        <option value="يهودي" <?php echo (($_POST['religion'] ?? '') == 'يهودي') ? 'selected' : ''; ?>>يهودي</option>
                        <option value="أخرى" <?php echo (($_POST['religion'] ?? '') == 'أخرى') ? 'selected' : ''; ?>>أخرى</option>
                    </select>
                </div>

                <div class="col-md-4 mb-3">
                    <label for="marital_status" class="form-label">الحالة الاجتماعية <span class="text-danger">*</span></label>
                    <select class="form-select <?php echo isset($errors['marital_status']) ? 'is-invalid' : ''; ?>"
                            id="marital_status"
                            name="marital_status"
                            onchange="toggleChildrenCount()"
                            required>
                        <option value="">اختر الحالة</option>
                        <option value="أعزب" <?php echo (($_POST['marital_status'] ?? '') == 'أعزب') ? 'selected' : ''; ?>>أعزب</option>
                        <option value="متزوج" <?php echo (($_POST['marital_status'] ?? '') == 'متزوج') ? 'selected' : ''; ?>>متزوج</option>
                        <option value="أرمل" <?php echo (($_POST['marital_status'] ?? '') == 'أرمل') ? 'selected' : ''; ?>>أرمل</option>
                        <option value="مطلق" <?php echo (($_POST['marital_status'] ?? '') == 'مطلق') ? 'selected' : ''; ?>>مطلق</option>
                        <option value="يعول" <?php echo (($_POST['marital_status'] ?? '') == 'يعول') ? 'selected' : ''; ?>>يعول</option>
                    </select>
                    <?php if (isset($errors['marital_status'])): ?>
                        <div class="invalid-feedback"><?php echo $errors['marital_status']; ?></div>
                    <?php endif; ?>
                </div>

                <div class="col-md-4 mb-3" id="children_count_container" style="display: none;">
                    <label for="children_count" class="form-label">عدد الأبناء</label>
                    <input type="number"
                           class="form-control"
                           id="children_count"
                           name="children_count"
                           min="0"
                           max="20"
                           value="<?php echo $_POST['children_count'] ?? '0'; ?>">
                    <div class="form-text">عدد الأطفال</div>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="insurance_number" class="form-label">الرقم التأميني</label>
                    <input type="text"
                           class="form-control <?php echo isset($errors['insurance_number']) ? 'is-invalid' : ''; ?>"
                           id="insurance_number"
                           name="insurance_number"
                           value="<?php echo $_POST['insurance_number'] ?? ''; ?>">
                    <?php if (isset($errors['insurance_number'])): ?>
                        <div class="invalid-feedback"><?php echo $errors['insurance_number']; ?></div>
                    <?php endif; ?>
                    <div class="form-text">رقم التأمين الاجتماعي</div>
                </div>

                <div class="col-md-6 mb-3">
                    <div class="form-check">
                        <input class="form-check-input"
                               type="checkbox"
                               id="has_disability"
                               name="has_disability"
                               value="1"
                               <?php echo isset($_POST['has_disability']) ? 'checked' : ''; ?>
                               onchange="toggleDisabilityType()">
                        <label class="form-check-label" for="has_disability">
                            يعاني من إعاقة
                        </label>
                    </div>
                </div>

                <div class="col-md-6 mb-3" id="disability_type_container" style="display: none;">
                    <label for="disability_type" class="form-label">نوع الإعاقة</label>
                    <input type="text"
                           class="form-control"
                           id="disability_type"
                           name="disability_type"
                           value="<?php echo $_POST['disability_type'] ?? ''; ?>"
                           placeholder="مثال: إعاقة حركية، إعاقة بصرية، إعاقة سمعية">
                </div>

                <div class="col-md-6 mb-3" id="disability_percentage_container" style="display: none;">
                    <label for="disability_percentage" class="form-label">نسبة الإعاقة</label>
                    <div class="input-group">
                        <input type="number"
                               class="form-control"
                               id="disability_percentage"
                               name="disability_percentage"
                               min="1"
                               max="100"
                               step="0.01"
                               value="<?php echo $_POST['disability_percentage'] ?? ''; ?>"
                               placeholder="مثال: 25.5">
                        <span class="input-group-text">%</span>
                    </div>
                    <div class="form-text">نسبة الإعاقة حسب التقرير الطبي</div>
                </div>
            </div>

            <!-- بيانات التجنيد والخدمة العامة -->
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="text-primary border-bottom pb-2 mb-3">
                        <i class="fas fa-shield-alt me-2"></i>بيانات التجنيد والخدمة العامة
                    </h6>
                </div>

                <!-- بيانات التجنيد للذكور -->
                <div class="col-12 mb-3" id="military_section" style="display: none;">
                    <div class="card border-info">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-user-shield me-2"></i>بيانات التجنيد (للذكور)
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="military_status" class="form-label">حالة التجنيد</label>
                                    <select class="form-select" id="military_status" name="military_status">
                                        <option value="غير محدد" <?php echo (($_POST['military_status'] ?? '') == 'غير محدد') ? 'selected' : ''; ?>>غير محدد</option>
                                        <option value="قام بالتجنيد" <?php echo (($_POST['military_status'] ?? '') == 'قام بالتجنيد') ? 'selected' : ''; ?>>قام بالتجنيد</option>
                                        <option value="إعفاء مؤقت" <?php echo (($_POST['military_status'] ?? '') == 'إعفاء مؤقت') ? 'selected' : ''; ?>>إعفاء مؤقت</option>
                                        <option value="إعفاء نهائي" <?php echo (($_POST['military_status'] ?? '') == 'إعفاء نهائي') ? 'selected' : ''; ?>>إعفاء نهائي</option>
                                        <option value="تأجيل التجنيد" <?php echo (($_POST['military_status'] ?? '') == 'تأجيل التجنيد') ? 'selected' : ''; ?>>تأجيل التجنيد</option>
                                    </select>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="military_status_date" class="form-label">تاريخ المعاملة</label>
                                    <input type="date"
                                           class="form-control"
                                           id="military_status_date"
                                           name="military_status_date"
                                           value="<?php echo $_POST['military_status_date'] ?? ''; ?>">
                                </div>

                                <div class="col-md-12 mb-3">
                                    <label for="military_notes" class="form-label">ملاحظات التجنيد</label>
                                    <textarea class="form-control"
                                              id="military_notes"
                                              name="military_notes"
                                              rows="3"
                                              placeholder="أي ملاحظات إضافية حول حالة التجنيد"><?php echo $_POST['military_notes'] ?? ''; ?></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- بيانات الخدمة العامة للإناث -->
                <div class="col-12 mb-3" id="public_service_section" style="display: none;">
                    <div class="card border-warning">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0">
                                <i class="fas fa-hands-helping me-2"></i>بيانات الخدمة العامة (للإناث)
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="public_service_status" class="form-label">حالة الخدمة العامة</label>
                                    <select class="form-select" id="public_service_status" name="public_service_status">
                                        <option value="غير محدد" <?php echo (($_POST['public_service_status'] ?? '') == 'غير محدد') ? 'selected' : ''; ?>>غير محدد</option>
                                        <option value="تأدية الخدمة" <?php echo (($_POST['public_service_status'] ?? '') == 'تأدية الخدمة') ? 'selected' : ''; ?>>تأدية الخدمة</option>
                                        <option value="لم تؤدى الخدمة" <?php echo (($_POST['public_service_status'] ?? '') == 'لم تؤدى الخدمة') ? 'selected' : ''; ?>>لم تؤدى الخدمة</option>
                                    </select>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="public_service_date" class="form-label">تاريخ المعاملة</label>
                                    <input type="date"
                                           class="form-control"
                                           id="public_service_date"
                                           name="public_service_date"
                                           value="<?php echo $_POST['public_service_date'] ?? ''; ?>">
                                </div>

                                <div class="col-md-12 mb-3">
                                    <label for="public_service_notes" class="form-label">ملاحظات الخدمة العامة</label>
                                    <textarea class="form-control"
                                              id="public_service_notes"
                                              name="public_service_notes"
                                              rows="3"
                                              placeholder="أي ملاحظات إضافية حول الخدمة العامة"><?php echo $_POST['public_service_notes'] ?? ''; ?></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- المؤهلات العلمية -->
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="text-primary border-bottom pb-2 mb-3">
                        <i class="fas fa-graduation-cap me-2"></i>المؤهلات العلمية
                    </h6>
                </div>



                <!-- منطقة إضافة المؤهلات -->
                <div class="col-12 mb-3">
                    <div class="card border-primary">
                        <div class="card-header bg-primary text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-plus me-2"></i>إضافة مؤهل علمي
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row" id="qualification-form">
                                <div class="col-md-4 mb-3">
                                    <label for="new_qualification_type" class="form-label">اختر المؤهل <span class="text-danger">*</span></label>
                                    <select class="form-select" id="new_qualification_type" required>
                                        <option value="">اختر المؤهل</option>
                                        <?php foreach (getQualifications() as $qual): ?>
                                            <option value="<?php echo $qual; ?>"><?php echo $qual; ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="col-md-3 mb-3">
                                    <label for="new_qualification_date" class="form-label">تاريخ الحصول <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="new_qualification_date" required>
                                </div>

                                <div class="col-md-3 mb-3">
                                    <label for="new_specialization" class="form-label">التخصص</label>
                                    <input type="text" class="form-control" id="new_specialization" placeholder="مثال: هندسة حاسوب">
                                </div>

                                <div class="col-md-2 mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="btn btn-success w-100" onclick="addQualification()">
                                        <i class="fas fa-plus"></i> إضافة
                                    </button>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="new_institution" class="form-label">المؤسسة التعليمية</label>
                                    <input type="text" class="form-control" id="new_institution" placeholder="مثال: جامعة القاهرة">
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="new_grade" class="form-label">التقدير</label>
                                    <select class="form-select" id="new_grade">
                                        <option value="">اختر التقدير</option>
                                        <option value="ممتاز">ممتاز</option>
                                        <option value="جيد جداً">جيد جداً</option>
                                        <option value="جيد">جيد</option>
                                        <option value="مقبول">مقبول</option>
                                        <option value="اجتياز">اجتياز</option>
                                        <option value="أخرى">أخرى</option>
                                    </select>
                                </div>

                                <div class="col-md-2 mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="btn btn-secondary w-100" onclick="clearQualificationForm()">
                                        <i class="fas fa-eraser"></i> مسح
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قائمة المؤهلات المضافة -->
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-list me-2"></i>المؤهلات المضافة (مرتبة حسب التاريخ)
                            </h6>
                        </div>
                        <div class="card-body">
                            <div id="qualifications-list">
                                <div class="text-muted text-center py-3">
                                    <i class="fas fa-graduation-cap fa-2x mb-2"></i>
                                    <p>لم يتم إضافة أي مؤهلات بعد</p>
                                    <small>استخدم النموذج أعلاه لإضافة المؤهلات العلمية</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- حقل مخفي لإرسال بيانات المؤهلات -->
                <input type="hidden" id="qualifications_data" name="qualifications_data" value="">

                <!-- عرض خطأ المؤهلات -->
                <?php if (isset($errors['qualifications'])): ?>
                    <div class="col-12">
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo $errors['qualifications']; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <div class="text-center">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-save me-2"></i>حفظ البيانات
                </button>
                <button type="reset" class="btn btn-outline-secondary btn-lg">
                    <i class="fas fa-undo me-2"></i>إعادة تعيين
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// التحقق من الرقم القومي أثناء الكتابة واستخراج البيانات
function validateAndExtractNationalId(input) {
    const value = input.value.replace(/\D/g, '');
    input.value = value;

    if (value.length === 14) {
        // التحقق من صحة الرقم القومي
        const isValid = validateEgyptianNationalId(value);
        if (isValid) {
            input.classList.remove('is-invalid');
            input.classList.add('is-valid');

            // استخراج البيانات من الرقم القومي
            extractDataFromNationalId(value);
        } else {
            input.classList.remove('is-valid');
            input.classList.add('is-invalid');
            clearExtractedData();
        }
    } else {
        input.classList.remove('is-valid', 'is-invalid');
        clearExtractedData();
    }
}

// استخراج البيانات من الرقم القومي
function extractDataFromNationalId(nationalId) {
    // استخراج تاريخ الميلاد
    const century = nationalId.charAt(0);
    const year = nationalId.substring(1, 3);
    const month = nationalId.substring(3, 5);
    const day = nationalId.substring(5, 7);

    const fullYear = (century === '2') ? '19' + year : '20' + year;
    const birthDate = fullYear + '-' + month + '-' + day;

    // حساب العمر
    const birth = new Date(birthDate);
    const today = new Date();
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--;
    }

    // استخراج النوع
    const genderDigit = parseInt(nationalId.charAt(12));
    const gender = (genderDigit % 2 === 0) ? 'أنثى' : 'ذكر';

    // استخراج محافظة الميلاد
    const governorateCode = parseInt(nationalId.substring(7, 9));
    const birthGovernorate = getGovernorateByCode(governorateCode);

    // استخراج الديانة (تقديري)
    const sequence = parseInt(nationalId.substring(9, 12));
    let religion = 'غير محدد';
    if (sequence >= 1 && sequence <= 199) {
        religion = 'مسلم';
    } else if (sequence >= 200 && sequence <= 299) {
        religion = 'مسيحي';
    } else if (sequence >= 300 && sequence <= 399) {
        religion = 'يهودي';
    }

    // ملء الحقول
    document.getElementById('birth_date').value = birthDate;
    document.getElementById('age').value = age;
    document.getElementById('birth_governorate').value = birthGovernorate;
    document.getElementById('gender').value = gender;
    document.getElementById('religion').value = religion;

    // تحديث أقسام التجنيد/الخدمة العامة حسب النوع
    toggleGenderSections();

    // إظهار رسالة نجاح
    showExtractionSuccess();
}

// مسح البيانات المستخرجة
function clearExtractedData() {
    document.getElementById('birth_date').value = '';
    document.getElementById('age').value = '';
    document.getElementById('birth_governorate').value = '';
}

// الحصول على اسم المحافظة بالكود
function getGovernorateByCode(code) {
    const codes = {
        1: 'القاهرة', 2: 'الإسكندرية', 3: 'بورسعيد', 4: 'السويس',
        11: 'دمياط', 12: 'الدقهلية', 13: 'الشرقية', 14: 'القليوبية',
        15: 'كفر الشيخ', 16: 'الغربية', 17: 'المنوفية', 18: 'البحيرة',
        19: 'الإسماعيلية', 21: 'الجيزة', 22: 'بني سويف', 23: 'الفيوم',
        24: 'المنيا', 25: 'أسيوط', 26: 'سوهاج', 27: 'قنا',
        28: 'أسوان', 29: 'الأقصر', 31: 'البحر الأحمر',
        32: 'الوادي الجديد', 33: 'مطروح', 34: 'شمال سيناء', 35: 'جنوب سيناء'
    };
    return codes[code] || 'غير محدد';
}

// إظهار رسالة نجاح الاستخراج
function showExtractionSuccess() {
    // إزالة أي رسائل سابقة
    const existingAlert = document.querySelector('#national_id').closest('.col-md-6').querySelector('.alert');
    if (existingAlert) {
        existingAlert.remove();
    }

    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show mt-2';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        تم استخراج البيانات من الرقم القومي بنجاح!
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const nationalIdContainer = document.querySelector('#national_id').closest('.col-md-6');
    nationalIdContainer.appendChild(alertDiv);

    // إزالة الرسالة بعد 4 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 4000);
}

function validateEgyptianNationalId(id) {
    if (id.length !== 14) return false;

    const century = id.charAt(0);
    if (century !== '2' && century !== '3') return false;

    const month = parseInt(id.substring(3, 5));
    if (month < 1 || month > 12) return false;

    const day = parseInt(id.substring(5, 7));
    if (day < 1 || day > 31) return false;

    const governorate = parseInt(id.substring(7, 9));
    if (governorate < 1 || governorate > 35) return false;

    return true;
}

// إظهار/إخفاء حقول الإعاقة
function toggleDisabilityType() {
    const checkbox = document.getElementById('has_disability');
    const typeContainer = document.getElementById('disability_type_container');
    const percentageContainer = document.getElementById('disability_percentage_container');

    if (checkbox.checked) {
        typeContainer.style.display = 'block';
        percentageContainer.style.display = 'block';
    } else {
        typeContainer.style.display = 'none';
        percentageContainer.style.display = 'none';
        document.getElementById('disability_type').value = '';
        document.getElementById('disability_percentage').value = '';
    }
}

// تنسيق رقم الهاتف
function formatPhone(input) {
    let value = input.value.replace(/\D/g, '');

    // التحقق من البداية الصحيحة
    if (value.length > 0 && !value.startsWith('01')) {
        if (value.startsWith('1')) {
            value = '0' + value;
        }
    }

    // تحديد الطول الأقصى
    if (value.length > 11) {
        value = value.substring(0, 11);
    }

    input.value = value;

    // التحقق من صحة الرقم
    if (value.length === 11 && value.startsWith('01')) {
        input.classList.remove('is-invalid');
        input.classList.add('is-valid');
    } else if (value.length > 0) {
        input.classList.remove('is-valid');
        input.classList.add('is-invalid');
    } else {
        input.classList.remove('is-valid', 'is-invalid');
    }
}

// التحقق من البريد الإلكتروني
function validateEmail(input) {
    const email = input.value;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (email && emailRegex.test(email)) {
        input.classList.remove('is-invalid');
        input.classList.add('is-valid');
    } else if (email) {
        input.classList.remove('is-valid');
        input.classList.add('is-invalid');
    } else {
        input.classList.remove('is-valid', 'is-invalid');
    }
}

// إظهار/إخفاء أقسام التجنيد والخدمة العامة حسب النوع
function toggleGenderSections() {
    const gender = document.getElementById('gender').value;
    const militarySection = document.getElementById('military_section');
    const publicServiceSection = document.getElementById('public_service_section');

    if (gender === 'ذكر') {
        militarySection.style.display = 'block';
        publicServiceSection.style.display = 'none';
        // إعادة تعيين قيم الخدمة العامة
        document.getElementById('public_service_status').value = 'غير محدد';
        document.getElementById('public_service_date').value = '';
        document.getElementById('public_service_notes').value = '';
    } else if (gender === 'أنثى') {
        militarySection.style.display = 'none';
        publicServiceSection.style.display = 'block';
        // إعادة تعيين قيم التجنيد
        document.getElementById('military_status').value = 'غير محدد';
        document.getElementById('military_status_date').value = '';
        document.getElementById('military_notes').value = '';
    } else {
        militarySection.style.display = 'none';
        publicServiceSection.style.display = 'none';
    }
}

// إظهار/إخفاء حقل عدد الأبناء حسب الحالة الاجتماعية
function toggleChildrenCount() {
    const maritalStatus = document.getElementById('marital_status').value;
    const childrenContainer = document.getElementById('children_count_container');
    const childrenInput = document.getElementById('children_count');

    if (maritalStatus === 'أعزب') {
        // إخفاء الحقل وتعيين القيمة إلى 0
        childrenContainer.style.display = 'none';
        childrenInput.value = '0';
    } else if (maritalStatus && maritalStatus !== '') {
        // إظهار الحقل للحالات الأخرى
        childrenContainer.style.display = 'block';
        // إذا كانت القيمة 0 وليس أعزب، اتركها فارغة ليدخل المستخدم القيمة
        if (childrenInput.value === '0') {
            childrenInput.value = '';
        }
    } else {
        // إخفاء الحقل إذا لم يتم اختيار حالة اجتماعية
        childrenContainer.style.display = 'none';
        childrenInput.value = '0';
    }
}

// متغير لتخزين المؤهلات
let qualifications = [];

// إضافة مؤهل جديد
function addQualification() {
    const type = document.getElementById('new_qualification_type').value;
    const date = document.getElementById('new_qualification_date').value;
    const specialization = document.getElementById('new_specialization').value;
    const institution = document.getElementById('new_institution').value;
    const grade = document.getElementById('new_grade').value;

    // التحقق من البيانات المطلوبة
    if (!type || !date) {
        alert('يرجى إدخال نوع المؤهل وتاريخ الحصول عليه');
        return;
    }

    // التحقق من عدم تكرار المؤهل
    const exists = qualifications.some(q => q.type === type && q.date === date);
    if (exists) {
        alert('هذا المؤهل مضاف بالفعل');
        return;
    }

    // إضافة المؤهل
    const qualification = {
        type: type,
        date: date,
        specialization: specialization || '',
        institution: institution || '',
        grade: grade || ''
    };

    qualifications.push(qualification);

    // ترتيب المؤهلات حسب التاريخ
    qualifications.sort((a, b) => new Date(a.date) - new Date(b.date));

    // تحديث العرض
    updateQualificationsList();
    updateHiddenFields();

    // مسح النموذج
    clearQualificationForm();
}

// مسح نموذج المؤهل
function clearQualificationForm() {
    document.getElementById('new_qualification_type').value = '';
    document.getElementById('new_qualification_date').value = '';
    document.getElementById('new_specialization').value = '';
    document.getElementById('new_institution').value = '';
    document.getElementById('new_grade').value = '';
}

// حذف مؤهل
function removeQualification(index) {
    if (confirm('هل أنت متأكد من حذف هذا المؤهل؟')) {
        qualifications.splice(index, 1);
        updateQualificationsList();
        updateHiddenFields();
    }
}

// تحديث قائمة المؤهلات
function updateQualificationsList() {
    const container = document.getElementById('qualifications-list');

    if (qualifications.length === 0) {
        container.innerHTML = `
            <div class="text-muted text-center py-3">
                <i class="fas fa-graduation-cap fa-2x mb-2"></i>
                <p>لم يتم إضافة أي مؤهلات بعد</p>
                <small>استخدم النموذج أعلاه لإضافة المؤهلات العلمية</small>
            </div>
        `;
        return;
    }

    let html = '';
    qualifications.forEach((qual, index) => {
        const formattedDate = new Date(qual.date).toLocaleDateString('ar-EG');
        html += `
            <div class="card mb-2">
                <div class="card-body py-2">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <strong class="text-primary">${qual.type}</strong>
                        </div>
                        <div class="col-md-2">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>${formattedDate}
                            </small>
                        </div>
                        <div class="col-md-3">
                            ${qual.specialization ? `<span class="badge bg-info">${qual.specialization}</span>` : '<span class="text-muted">-</span>'}
                        </div>
                        <div class="col-md-3">
                            ${qual.institution ? `<small>${qual.institution}</small>` : '<span class="text-muted">-</span>'}
                        </div>
                        <div class="col-md-1">
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeQualification(${index})" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    ${qual.grade ? `<div class="row mt-1"><div class="col-12"><small class="text-success"><i class="fas fa-award me-1"></i>التقدير: ${qual.grade}</small></div></div>` : ''}
                </div>
            </div>
        `;
    });

    container.innerHTML = html;
}

// تحديث الحقول المخفية
function updateHiddenFields() {
    // تحديث بيانات المؤهلات
    document.getElementById('qualifications_data').value = JSON.stringify(qualifications);
}

// معاينة الصورة
function previewPhoto(input) {
    const file = input.files[0];
    const preview = document.getElementById('photo-preview');
    const previewImage = document.getElementById('preview-image');

    if (file) {
        // التحقق من نوع الملف
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            alert('نوع الملف غير مدعوم. يرجى اختيار صورة بصيغة JPG, PNG, GIF أو WEBP');
            input.value = '';
            return;
        }

        // التحقق من حجم الملف (5MB)
        if (file.size > 5 * 1024 * 1024) {
            alert('حجم الملف كبير جداً. الحد الأقصى 5MB');
            input.value = '';
            return;
        }

        // عرض معاينة الصورة
        const reader = new FileReader();
        reader.onload = function(e) {
            previewImage.src = e.target.result;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    } else {
        preview.style.display = 'none';
    }
}

// إزالة معاينة الصورة
function removePhotoPreview() {
    document.getElementById('photo').value = '';
    document.getElementById('photo-preview').style.display = 'none';
}

// معاينة صورة الرقم القومي
function previewNationalIdPhoto(input) {
    const file = input.files[0];
    const preview = document.getElementById('national-id-photo-preview');
    const previewImage = document.getElementById('national-id-preview-image');

    if (file) {
        // التحقق من نوع الملف
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'application/pdf'];
        if (!allowedTypes.includes(file.type)) {
            alert('نوع الملف غير مدعوم. يرجى اختيار صورة بصيغة JPG, PNG, GIF, WEBP أو PDF');
            input.value = '';
            return;
        }

        // التحقق من حجم الملف (10MB)
        if (file.size > 10 * 1024 * 1024) {
            alert('حجم الملف كبير جداً. الحد الأقصى 10MB');
            input.value = '';
            return;
        }

        // عرض معاينة الصورة (للصور فقط، ليس PDF)
        if (file.type !== 'application/pdf') {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImage.src = e.target.result;
                preview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            // للملفات PDF، عرض أيقونة PDF
            previewImage.src = 'data:image/svg+xml;base64,' + btoa(`
                <svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
                    <rect width="200" height="150" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
                    <text x="100" y="75" text-anchor="middle" font-family="Arial" font-size="16" fill="#dc3545">PDF</text>
                    <text x="100" y="95" text-anchor="middle" font-family="Arial" font-size="12" fill="#6c757d">ملف PDF</text>
                </svg>
            `);
            preview.style.display = 'block';
        }
    } else {
        preview.style.display = 'none';
    }
}

// إزالة معاينة صورة الرقم القومي
function removeNationalIdPhotoPreview() {
    document.getElementById('national_id_photo').value = '';
    document.getElementById('national-id-photo-preview').style.display = 'none';
}

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إظهار نوع الإعاقة إذا كان محدد
    <?php if (isset($_POST['has_disability'])): ?>
    toggleDisabilityType();
    <?php endif; ?>

    // إظهار أقسام التجنيد/الخدمة العامة حسب النوع المحدد
    <?php if (isset($_POST['gender'])): ?>
    toggleGenderSections();
    <?php endif; ?>

    // إظهار حقل عدد الأبناء حسب الحالة الاجتماعية المحددة
    <?php if (isset($_POST['marital_status'])): ?>
    toggleChildrenCount();
    <?php endif; ?>

    // إضافة مستمعي الأحداث
    document.getElementById('gender').addEventListener('change', toggleGenderSections);
    document.getElementById('marital_status').addEventListener('change', toggleChildrenCount);
    document.getElementById('photo').addEventListener('change', function() {
        previewPhoto(this);
    });
    document.getElementById('national_id_photo').addEventListener('change', function() {
        previewNationalIdPhoto(this);
    });

    document.getElementById('email').addEventListener('blur', function() {
        validateEmail(this);
    });

    // التحقق من النموذج قبل الإرسال
    document.querySelector('form').addEventListener('submit', function(e) {
        const nationalId = document.getElementById('national_id').value;
        const phone = document.getElementById('phone').value;

        if (!validateEgyptianNationalId(nationalId)) {
            e.preventDefault();
            alert('الرقم القومي غير صحيح');
            document.getElementById('national_id').focus();
            return false;
        }

        if (phone && !phone.match(/^01[0-9]{9}$/)) {
            e.preventDefault();
            alert('رقم الهاتف غير صحيح');
            document.getElementById('phone').focus();
            return false;
        }
    });

    // إدارة قائمة الأحياء حسب المحافظة
    const districtsData = <?php echo json_encode(getDistrictsByGovernorate(), JSON_UNESCAPED_UNICODE); ?>;
    const governorateSelect = document.getElementById('governorate');
    const citySelect = document.getElementById('city');
    const selectedCity = '<?php echo $_POST['city'] ?? ''; ?>';

    function updateCityOptions() {
        const selectedGovernorate = governorateSelect.value;

        // مسح الخيارات الحالية
        citySelect.innerHTML = '<option value="">اختر الحي/المدينة</option>';

        if (selectedGovernorate && districtsData[selectedGovernorate]) {
            // إضافة الأحياء الخاصة بالمحافظة المختارة
            districtsData[selectedGovernorate].forEach(function(district) {
                const option = document.createElement('option');
                option.value = district;
                option.textContent = district;

                // تحديد الخيار المحفوظ مسبقاً
                if (district === selectedCity) {
                    option.selected = true;
                }

                citySelect.appendChild(option);
            });
        }
    }

    // تحديث قائمة الأحياء عند تغيير المحافظة
    governorateSelect.addEventListener('change', updateCityOptions);

    // تحديث قائمة الأحياء عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        updateCityOptions();
    });

    // إضافة حي جديد
    document.getElementById('addCustomCity').addEventListener('click', function() {
        const selectedGovernorate = governorateSelect.value;

        if (!selectedGovernorate) {
            alert('يرجى اختيار المحافظة أولاً');
            governorateSelect.focus();
            return;
        }

        const customCity = prompt('أدخل اسم الحي/المدينة الجديد:');

        if (customCity && customCity.trim()) {
            const trimmedCity = customCity.trim();

            // التحقق من عدم وجود الحي بالفعل
            const existingOptions = Array.from(citySelect.options);
            const exists = existingOptions.some(option => option.value === trimmedCity);

            if (!exists) {
                // إضافة الخيار الجديد
                const newOption = document.createElement('option');
                newOption.value = trimmedCity;
                newOption.textContent = trimmedCity;
                newOption.selected = true;

                citySelect.appendChild(newOption);

                // إظهار رسالة تأكيد
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-info alert-dismissible fade show mt-2';
                alertDiv.innerHTML = `
                    <i class="fas fa-info-circle me-2"></i>
                    تم إضافة "${trimmedCity}" كحي جديد في ${selectedGovernorate}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                document.querySelector('.col-md-6:has(#city)').appendChild(alertDiv);

                // إزالة الرسالة بعد 5 ثوان
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            } else {
                alert('هذا الحي موجود بالفعل في القائمة');
                // تحديد الحي الموجود
                citySelect.value = trimmedCity;
            }
        }
    });
});
</script>

<?php include '../includes/footer.php'; ?>
