-- نظام إدارة بيانات الموظفين
-- Employee Management System Database

CREATE DATABASE IF NOT EXISTS employee_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE employee_management;

-- ج<PERSON><PERSON><PERSON> الموظفين
CREATE TABLE employees (
    id INT AUTO_INCREMENT PRIMARY KEY,
    employee_code VARCHAR(20) UNIQUE NOT NULL,
    
    -- ال<PERSON>س<PERSON> الخماسي
    first_name VARCHAR(50) NOT NULL,
    father_name VARCHAR(50) NOT NULL,
    grandfather_name VARCHAR(50) NOT NULL,
    family_name VARCHAR(50) NOT NULL,
    nickname VA<PERSON><PERSON><PERSON>(50),
    
    -- البيانات الأساسية
    national_id VARCHAR(14) UNIQUE NOT NULL,
    insurance_number VARCHAR(20) UNIQUE,
    
    -- العنوان
    country VARCHAR(50) DEFAULT 'مصر',
    governorate VARCHAR(50) NOT NULL,
    city VARCHAR(50) NOT NULL,
    street VARCHAR(100),
    postal_code VARCHAR(10),
    
    -- بيانات الاتصال
    phone VARCHAR(15) NOT NULL,
    email VARCHAR(100) UNIQUE,
    
    -- البيانات الشخصية
    gender ENUM('ذكر', 'أنثى') NOT NULL,
    religion VARCHAR(30),
    marital_status ENUM('أعزب', 'متزوج', 'أرمل', 'مطلق', 'يعول') NOT NULL,
    
    -- الإعاقة
    has_disability BOOLEAN DEFAULT FALSE,
    disability_type VARCHAR(100),

    -- بيانات التجنيد للذكور
    military_status ENUM('قام بالتجنيد', 'إعفاء مؤقت', 'إعفاء نهائي', 'تأجيل التجنيد', 'غير محدد') DEFAULT 'غير محدد',
    military_status_date DATE,
    military_notes TEXT,

    -- بيانات الخدمة العامة للإناث
    public_service_status ENUM('تأدية الخدمة', 'لم تؤدى الخدمة', 'غير محدد') DEFAULT 'غير محدد',
    public_service_date DATE,
    public_service_notes TEXT,

    -- المؤهلات
    qualification VARCHAR(100) NOT NULL,
    qualification_date DATE,
    highest_qualification VARCHAR(100),
    highest_qualification_date DATE,
    specialization VARCHAR(100),
    
    -- تواريخ النظام
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    
    -- فهارس
    INDEX idx_employee_code (employee_code),
    INDEX idx_national_id (national_id),
    INDEX idx_name (first_name, father_name, family_name),
    INDEX idx_qualification (qualification),
    INDEX idx_specialization (specialization)
);

-- جدول المستخدمين والصلاحيات
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    role ENUM('admin', 'hr_manager', 'hr_employee', 'viewer') DEFAULT 'viewer',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL
);

-- جدول سجل التعديلات
CREATE TABLE audit_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    employee_id INT,
    user_id INT,
    action ENUM('INSERT', 'UPDATE', 'DELETE') NOT NULL,
    field_name VARCHAR(50),
    old_value TEXT,
    new_value TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- إدراج مستخدم افتراضي (admin)
INSERT INTO users (username, password, full_name, email, role) 
VALUES ('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', '<EMAIL>', 'admin');

-- إدراج بيانات تجريبية
INSERT INTO employees (
    employee_code, first_name, father_name, grandfather_name, family_name, nickname,
    national_id, insurance_number, governorate, city, street, phone, email,
    gender, religion, marital_status, has_disability,
    military_status, military_status_date, military_notes,
    public_service_status, public_service_date, public_service_notes,
    qualification, qualification_date, specialization, created_by
) VALUES
(
    'EMP001', 'أحمد', 'محمد', 'علي', 'السيد', 'أبو محمد',
    '12345678901234', 'INS001', 'القاهرة', 'مدينة نصر', 'شارع مصطفى النحاس', '01012345678', '<EMAIL>',
    'ذكر', 'مسلم', 'متزوج', FALSE,
    'قام بالتجنيد', '2014-03-15', 'أدى الخدمة العسكرية الإلزامية لمدة سنة',
    'غير محدد', NULL, NULL,
    'بكالوريوس هندسة', '2015-06-15', 'هندسة حاسوب', 1
),
(
    'EMP002', 'فاطمة', 'أحمد', 'محمود', 'عبدالله', '',
    '98765432109876', 'INS002', 'الجيزة', 'الدقي', 'شارع التحرير', '01098765432', '<EMAIL>',
    'أنثى', 'مسلم', 'أعزب', FALSE,
    'غير محدد', NULL, NULL,
    'تأدية الخدمة', '2019-08-10', 'أدت الخدمة العامة في وزارة التربية والتعليم',
    'بكالوريوس تجارة', '2018-07-20', 'محاسبة', 1
),
(
    'EMP003', 'محمد', 'علي', 'حسن', 'إبراهيم', '',
    '29512345678901', 'INS003', 'الإسكندرية', 'سيدي جابر', 'شارع الجيش', '01123456789', '<EMAIL>',
    'ذكر', 'مسلم', 'أعزب', FALSE,
    'إعفاء نهائي', '2020-01-20', 'إعفاء نهائي لأسباب طبية',
    'غير محدد', NULL, NULL,
    'دبلوم فني', '2019-06-30', 'كهرباء', 1
),
(
    'EMP004', 'مريم', 'سعد', 'أحمد', 'محمود', '',
    '29612345678902', 'INS004', 'القاهرة', 'مصر الجديدة', 'شارع الحجاز', '01234567890', '<EMAIL>',
    'أنثى', 'مسيحي', 'متزوج', FALSE,
    'غير محدد', NULL, NULL,
    'لم تؤدى الخدمة', NULL, 'لم تؤدي الخدمة العامة بعد',
    'ليسانس آداب', '2020-07-15', 'لغة إنجليزية', 1
);

-- إنشاء فيو لعرض البيانات الكاملة
CREATE VIEW employee_full_view AS
SELECT 
    e.*,
    CONCAT(e.first_name, ' ', e.father_name, ' ', e.grandfather_name, ' ', e.family_name) as full_name,
    u1.full_name as created_by_name,
    u2.full_name as updated_by_name
FROM employees e
LEFT JOIN users u1 ON e.created_by = u1.id
LEFT JOIN users u2 ON e.updated_by = u2.id;
