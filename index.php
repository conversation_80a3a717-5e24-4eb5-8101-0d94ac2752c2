<?php
/**
 * الصفحة الرئيسية - لوحة التحكم
 * Main Dashboard Page
 */

require_once 'includes/config.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
requireLogin();

$page_title = 'لوحة التحكم';
$db = getDatabase();

// دالة لتوليد ألوان جميلة للرسم البياني
function getQualificationColor($index) {
    $colors = [
        '#FF6384', // وردي
        '#36A2EB', // أزرق
        '#FFCE56', // أصفر
        '#4BC0C0', // تركوازي
        '#9966FF', // بنفسجي
        '#FF9F40', // برتقالي
        '#FF6384', // وردي فاتح
        '#C9CBCF', // رمادي
        '#4BC0C0', // أخضر مائي
        '#FF6384'  // أحمر فاتح
    ];
    return $colors[$index % count($colors)];
}

// دالة لتوليد ألوان التخصصات
function getSpecializationColor($index) {
    $colors = [
        '#28a745', // أخضر
        '#17a2b8', // تركوازي
        '#ffc107', // أصفر
        '#dc3545', // أحمر
        '#6f42c1', // بنفسجي
        '#fd7e14', // برتقالي
        '#20c997', // أخضر مائي
        '#6c757d', // رمادي
        '#e83e8c', // وردي
        '#007bff'  // أزرق
    ];
    return $colors[$index % count($colors)];
}

// دالة لتوليد ألوان التجنيد
function getMilitaryColor($index) {
    $colors = [
        '#17a2b8', // تركوازي
        '#28a745', // أخضر
        '#ffc107', // أصفر
        '#dc3545', // أحمر
        '#6f42c1', // بنفسجي
        '#fd7e14', // برتقالي
        '#20c997', // أخضر مائي
        '#6c757d'  // رمادي
    ];
    return $colors[$index % count($colors)];
}

// دالة لتوليد ألوان الخدمة العامة
function getPublicServiceColor($index) {
    $colors = [
        '#ffc107', // أصفر
        '#fd7e14', // برتقالي
        '#28a745', // أخضر
        '#dc3545', // أحمر
        '#6f42c1', // بنفسجي
        '#17a2b8', // تركوازي
        '#20c997', // أخضر مائي
        '#6c757d'  // رمادي
    ];
    return $colors[$index % count($colors)];
}

// إحصائيات سريعة
$total_employees = $db->count('employees');
$male_employees = $db->count('employees', 'gender = ?', ['ذكر']);
$female_employees = $db->count('employees', 'gender = ?', ['أنثى']);
$married_employees = $db->count('employees', 'marital_status = ?', ['متزوج']);
$disabled_employees = $db->count('employees', 'has_disability = ?', [1]);

// إحصائيات تحذيرات الرقم القومي
$expired_national_ids = $db->fetchColumn("
    SELECT COUNT(*) FROM employees
    WHERE national_id_expiry_date IS NOT NULL
    AND national_id_expiry_date < CURDATE()
");

$expiring_soon_national_ids = $db->fetchColumn("
    SELECT COUNT(*) FROM employees
    WHERE national_id_expiry_date IS NOT NULL
    AND national_id_expiry_date >= CURDATE()
    AND DATEDIFF(national_id_expiry_date, CURDATE()) <= 30
");

// إحصائيات تحذيرات شهادات التجنيد/الخدمة العامة
$expired_service_certificates = $db->fetchColumn("
    SELECT COUNT(*) FROM employees
    WHERE service_certificate_expiry_date IS NOT NULL
    AND service_certificate_expiry_date < CURDATE()
");

$expiring_soon_service_certificates = $db->fetchColumn("
    SELECT COUNT(*) FROM employees
    WHERE service_certificate_expiry_date IS NOT NULL
    AND service_certificate_expiry_date >= CURDATE()
    AND DATEDIFF(service_certificate_expiry_date, CURDATE()) <= 30
");

// الموظفون المضافون حديثاً
$recent_employees = $db->fetchAll(
    "SELECT * FROM employee_full_view ORDER BY created_at DESC LIMIT 5"
);

// إحصائيات المؤهلات
$qualification_stats = $db->fetchAll(
    "SELECT qualification, COUNT(*) as count 
     FROM employees 
     GROUP BY qualification 
     ORDER BY count DESC 
     LIMIT 5"
);

// إحصائيات التخصصات
$specialization_stats = $db->fetchAll(
    "SELECT specialization, COUNT(*) as count
     FROM employees
     WHERE specialization IS NOT NULL AND specialization != ''
     GROUP BY specialization
     ORDER BY count DESC
     LIMIT 5"
);

// إحصائيات التجنيد للذكور
$military_stats = $db->fetchAll(
    "SELECT military_status, COUNT(*) as count
     FROM employees
     WHERE gender = 'ذكر' AND military_status != 'غير محدد'
     GROUP BY military_status
     ORDER BY count DESC"
);

// إحصائيات الخدمة العامة للإناث
$public_service_stats = $db->fetchAll(
    "SELECT public_service_status, COUNT(*) as count
     FROM employees
     WHERE gender = 'أنثى' AND public_service_status != 'غير محدد'
     GROUP BY public_service_status
     ORDER BY count DESC"
);

include 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt me-2"></i>
        لوحة التحكم
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshPage()">
                <i class="fas fa-sync-alt"></i> تحديث
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="printPage()">
                <i class="fas fa-print"></i> طباعة
            </button>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="stats-number"><?php echo number_format($total_employees); ?></div>
                    <div class="stats-label">إجمالي الموظفين</div>
                </div>
                <div class="flex-shrink-0">
                    <i class="fas fa-users fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="stats-number"><?php echo number_format($male_employees); ?></div>
                    <div class="stats-label">الموظفين الذكور</div>
                </div>
                <div class="flex-shrink-0">
                    <i class="fas fa-male fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="stats-number"><?php echo number_format($female_employees); ?></div>
                    <div class="stats-label">الموظفات الإناث</div>
                </div>
                <div class="flex-shrink-0">
                    <i class="fas fa-female fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); color: #333;">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="stats-number"><?php echo number_format($married_employees); ?></div>
                    <div class="stats-label">المتزوجين</div>
                </div>
                <div class="flex-shrink-0">
                    <i class="fas fa-heart fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- تحذيرات الرقم القومي -->
<?php if ($expired_national_ids > 0 || $expiring_soon_national_ids > 0): ?>
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-warning border-start border-warning border-4" role="alert">
            <div class="d-flex align-items-center">
                <i class="fas fa-exclamation-triangle fa-2x text-warning me-3"></i>
                <div class="flex-grow-1">
                    <h5 class="alert-heading mb-2">تحذيرات الرقم القومي</h5>
                    <div class="row">
                        <?php if ($expired_national_ids > 0): ?>
                        <div class="col-md-6">
                            <span class="badge bg-danger me-2"><?php echo $expired_national_ids; ?></span>
                            موظف لديه رقم قومي منتهي الصلاحية
                        </div>
                        <?php endif; ?>
                        <?php if ($expiring_soon_national_ids > 0): ?>
                        <div class="col-md-6">
                            <span class="badge bg-warning text-dark me-2"><?php echo $expiring_soon_national_ids; ?></span>
                            موظف ينتهي رقمه القومي خلال 30 يوم
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <div>
                    <a href="pages/national_id_expiry_alerts.php" class="btn btn-warning">
                        <i class="fas fa-eye me-1"></i>عرض التفاصيل
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- تحذيرات شهادات التجنيد/الخدمة العامة -->
<?php if ($expired_service_certificates > 0 || $expiring_soon_service_certificates > 0): ?>
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-info border-start border-info border-4" role="alert">
            <div class="d-flex align-items-center">
                <i class="fas fa-certificate fa-2x text-info me-3"></i>
                <div class="flex-grow-1">
                    <h5 class="alert-heading mb-2">تحذيرات شهادات التجنيد/الخدمة العامة</h5>
                    <div class="row">
                        <?php if ($expired_service_certificates > 0): ?>
                        <div class="col-md-6">
                            <span class="badge bg-danger me-2"><?php echo $expired_service_certificates; ?></span>
                            شهادة منتهية الصلاحية
                        </div>
                        <?php endif; ?>
                        <?php if ($expiring_soon_service_certificates > 0): ?>
                        <div class="col-md-6">
                            <span class="badge bg-warning text-dark me-2"><?php echo $expiring_soon_service_certificates; ?></span>
                            شهادة تنتهي خلال 30 يوم
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <div>
                    <a href="pages/service_certificate_expiry_alerts.php" class="btn btn-info">
                        <i class="fas fa-eye me-1"></i>عرض التفاصيل
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="row">
    <!-- الموظفون المضافون حديثاً -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-plus me-2"></i>
                    الموظفون المضافون حديثاً
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($recent_employees)): ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-users fa-3x mb-3"></i>
                        <p>لا توجد بيانات موظفين</p>
                        <?php if (hasPermission('hr_employee')): ?>
                            <a href="pages/add_employee.php" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>إضافة موظف جديد
                            </a>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>كود الموظف</th>
                                    <th>الاسم</th>
                                    <th>المؤهل</th>
                                    <th>التخصص</th>
                                    <th>تاريخ الإضافة</th>
                                    <th>العمليات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_employees as $employee): ?>
                                <tr>
                                    <td>
                                        <span class="badge bg-primary"><?php echo $employee['employee_code']; ?></span>
                                    </td>
                                    <td>
                                        <strong><?php echo $employee['full_name']; ?></strong>
                                    </td>
                                    <td><?php echo $employee['qualification']; ?></td>
                                    <td><?php echo $employee['specialization'] ?: '-'; ?></td>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo formatDateTime($employee['created_at'], 'Y-m-d H:i'); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <a href="pages/view_employee.php?id=<?php echo $employee['id']; ?>" 
                                           class="btn btn-sm btn-outline-primary" 
                                           data-bs-toggle="tooltip" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php if (hasPermission('hr_employee')): ?>
                                        <a href="pages/edit_employee.php?id=<?php echo $employee['id']; ?>" 
                                           class="btn btn-sm btn-outline-warning" 
                                           data-bs-toggle="tooltip" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center">
                        <a href="pages/view_employees.php" class="btn btn-outline-primary">
                            عرض جميع الموظفين <i class="fas fa-arrow-left ms-2"></i>
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- إحصائيات جانبية -->
    <div class="col-lg-4">
        <!-- إحصائيات النوع -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-users me-2"></i>
                    توزيع الموظفين حسب النوع
                </h6>
            </div>
            <div class="card-body">
                <!-- الرسم البياني الدائري للنوع -->
                <div class="chart-container" style="position: relative; height: 200px; margin-bottom: 15px;">
                    <canvas id="genderChart"></canvas>
                </div>

                <!-- الأسطورة التفصيلية للنوع -->
                <div class="gender-legend">
                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 rounded" style="background: rgba(0,0,0,0.05);">
                        <div class="d-flex align-items-center">
                            <div class="legend-color me-2"
                                 style="width: 16px; height: 16px; border-radius: 50%; background-color: #36A2EB"></div>
                            <span class="fw-bold">ذكور</span>
                        </div>
                        <div>
                            <span class="badge bg-info"><?php echo $male_employees; ?></span>
                            <small class="text-muted ms-1">
                                (<?php echo $total_employees > 0 ? round(($male_employees / $total_employees) * 100, 1) : 0; ?>%)
                            </small>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 rounded" style="background: rgba(0,0,0,0.05);">
                        <div class="d-flex align-items-center">
                            <div class="legend-color me-2"
                                 style="width: 16px; height: 16px; border-radius: 50%; background-color: #FF6384"></div>
                            <span class="fw-bold">إناث</span>
                        </div>
                        <div>
                            <span class="badge bg-pink"><?php echo $female_employees; ?></span>
                            <small class="text-muted ms-1">
                                (<?php echo $total_employees > 0 ? round(($female_employees / $total_employees) * 100, 1) : 0; ?>%)
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات المؤهلات -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-graduation-cap me-2"></i>
                    توزيع المؤهلات
                </h6>
            </div>
            <div class="card-body">
                <?php if (empty($qualification_stats)): ?>
                    <p class="text-muted text-center">لا توجد بيانات</p>
                <?php else: ?>
                    <!-- الرسم البياني الدائري -->
                    <div class="chart-container" style="position: relative; height: 300px; margin-bottom: 20px;">
                        <canvas id="qualificationChart"></canvas>
                    </div>

                    <!-- الأسطورة التفصيلية -->
                    <div class="qualification-legend">
                        <?php foreach ($qualification_stats as $index => $stat): ?>
                        <div class="d-flex justify-content-between align-items-center mb-2 p-2 rounded" style="background: rgba(0,0,0,0.05);">
                            <div class="d-flex align-items-center">
                                <div class="legend-color me-2"
                                     style="width: 16px; height: 16px; border-radius: 50%; background-color: <?php echo getQualificationColor($index); ?>"></div>
                                <span class="fw-bold"><?php echo $stat['qualification']; ?></span>
                            </div>
                            <div>
                                <span class="badge bg-primary"><?php echo $stat['count']; ?></span>
                                <small class="text-muted ms-1">
                                    (<?php echo round(($stat['count'] / $total_employees) * 100, 1); ?>%)
                                </small>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- إحصائيات التخصصات -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-briefcase me-2"></i>
                    أهم التخصصات
                </h6>
            </div>
            <div class="card-body">
                <?php if (empty($specialization_stats)): ?>
                    <p class="text-muted text-center">لا توجد بيانات</p>
                <?php else: ?>
                    <!-- الرسم البياني الدائري للتخصصات -->
                    <div class="chart-container" style="position: relative; height: 250px; margin-bottom: 15px;">
                        <canvas id="specializationChart"></canvas>
                    </div>

                    <!-- الأسطورة التفصيلية للتخصصات -->
                    <div class="specialization-legend">
                        <?php foreach ($specialization_stats as $index => $stat): ?>
                        <div class="d-flex justify-content-between align-items-center mb-1 p-2 rounded" style="background: rgba(0,0,0,0.05);">
                            <div class="d-flex align-items-center">
                                <div class="legend-color me-2"
                                     style="width: 12px; height: 12px; border-radius: 50%; background-color: <?php echo getSpecializationColor($index); ?>"></div>
                                <span class="fw-bold" style="font-size: 0.85rem;"><?php echo $stat['specialization']; ?></span>
                            </div>
                            <span class="badge bg-success"><?php echo $stat['count']; ?></span>
                        </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- إحصائيات التجنيد -->
        <?php if (!empty($military_stats)): ?>
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-user-shield me-2"></i>
                    إحصائيات التجنيد (الذكور)
                </h6>
            </div>
            <div class="card-body">
                <!-- الرسم البياني الدائري للتجنيد -->
                <div class="chart-container" style="position: relative; height: 220px; margin-bottom: 15px;">
                    <canvas id="militaryChart"></canvas>
                </div>

                <!-- الأسطورة التفصيلية للتجنيد -->
                <div class="military-legend">
                    <?php foreach ($military_stats as $index => $stat): ?>
                    <div class="d-flex justify-content-between align-items-center mb-1 p-2 rounded" style="background: rgba(0,0,0,0.05);">
                        <div class="d-flex align-items-center">
                            <div class="legend-color me-2"
                                 style="width: 12px; height: 12px; border-radius: 50%; background-color: <?php echo getMilitaryColor($index); ?>"></div>
                            <span class="fw-bold" style="font-size: 0.85rem;"><?php echo $stat['military_status']; ?></span>
                        </div>
                        <span class="badge bg-info"><?php echo $stat['count']; ?></span>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- إحصائيات الخدمة العامة -->
        <?php if (!empty($public_service_stats)): ?>
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <h6 class="card-title mb-0">
                    <i class="fas fa-hands-helping me-2"></i>
                    إحصائيات الخدمة العامة (الإناث)
                </h6>
            </div>
            <div class="card-body">
                <!-- الرسم البياني الدائري للخدمة العامة -->
                <div class="chart-container" style="position: relative; height: 220px; margin-bottom: 15px;">
                    <canvas id="publicServiceChart"></canvas>
                </div>

                <!-- الأسطورة التفصيلية للخدمة العامة -->
                <div class="public-service-legend">
                    <?php foreach ($public_service_stats as $index => $stat): ?>
                    <div class="d-flex justify-content-between align-items-center mb-1 p-2 rounded" style="background: rgba(0,0,0,0.05);">
                        <div class="d-flex align-items-center">
                            <div class="legend-color me-2"
                                 style="width: 12px; height: 12px; border-radius: 50%; background-color: <?php echo getPublicServiceColor($index); ?>"></div>
                            <span class="fw-bold" style="font-size: 0.85rem;"><?php echo $stat['public_service_status']; ?></span>
                        </div>
                        <span class="badge bg-warning text-dark"><?php echo $stat['count']; ?></span>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- روابط سريعة -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    روابط سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <?php if (hasPermission('hr_employee')): ?>
                    <a href="pages/add_employee.php" class="btn btn-primary btn-sm">
                        <i class="fas fa-user-plus me-2"></i>إضافة موظف جديد
                    </a>
                    <?php endif; ?>
                    
                    <a href="pages/search.php" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-search me-2"></i>البحث المتقدم
                    </a>
                    
                    <a href="pages/reports.php" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-chart-bar me-2"></i>التقارير
                    </a>
                    
                    <?php if (hasPermission('admin')): ?>
                    <a href="pages/backup.php" class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-database me-2"></i>النسخ الاحتياطي
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js Library -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// رسم بياني دائري للمؤهلات
<?php if (!empty($qualification_stats)): ?>
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('qualificationChart').getContext('2d');

    // بيانات المؤهلات
    const qualificationData = {
        labels: [
            <?php foreach ($qualification_stats as $stat): ?>
            '<?php echo addslashes($stat['qualification']); ?>',
            <?php endforeach; ?>
        ],
        datasets: [{
            data: [
                <?php foreach ($qualification_stats as $stat): ?>
                <?php echo $stat['count']; ?>,
                <?php endforeach; ?>
            ],
            backgroundColor: [
                <?php foreach ($qualification_stats as $index => $stat): ?>
                '<?php echo getQualificationColor($index); ?>',
                <?php endforeach; ?>
            ],
            borderColor: [
                <?php foreach ($qualification_stats as $index => $stat): ?>
                '<?php echo getQualificationColor($index); ?>',
                <?php endforeach; ?>
            ],
            borderWidth: 2,
            hoverBorderWidth: 4,
            hoverBorderColor: '#fff'
        }]
    };

    // إعدادات الرسم البياني
    const config = {
        type: 'doughnut',
        data: qualificationData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false // إخفاء الأسطورة الافتراضية
                },
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#fff',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: true,
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${label}: ${value} موظف (${percentage}%)`;
                        }
                    }
                }
            },
            animation: {
                animateRotate: true,
                animateScale: true,
                duration: 2000,
                easing: 'easeOutQuart'
            },
            cutout: '60%', // لجعل الدائرة مجوفة (دونات)
            elements: {
                arc: {
                    borderWidth: 2,
                    hoverBorderWidth: 4
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    };

    // إنشاء الرسم البياني
    const qualificationChart = new Chart(ctx, config);

    // إضافة تأثيرات تفاعلية
    ctx.canvas.addEventListener('mousemove', function(e) {
        const points = qualificationChart.getElementsAtEventForMode(e, 'nearest', { intersect: true }, true);

        if (points.length) {
            ctx.canvas.style.cursor = 'pointer';
        } else {
            ctx.canvas.style.cursor = 'default';
        }
    });

    // إضافة نص في المنتصف
    Chart.register({
        id: 'centerText',
        beforeDraw: function(chart) {
            if (chart.config.type === 'doughnut') {
                const ctx = chart.ctx;
                const centerX = (chart.chartArea.left + chart.chartArea.right) / 2;
                const centerY = (chart.chartArea.top + chart.chartArea.bottom) / 2;

                ctx.save();
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';

                // النص الرئيسي
                ctx.font = 'bold 24px Cairo, sans-serif';
                ctx.fillStyle = '#333';
                ctx.fillText('<?php echo $total_employees; ?>', centerX, centerY - 10);

                // النص الفرعي
                ctx.font = '14px Cairo, sans-serif';
                ctx.fillStyle = '#666';
                ctx.fillText('إجمالي الموظفين', centerX, centerY + 15);

                ctx.restore();
            }
        }
    });
});
<?php endif; ?>

// رسم بياني دائري للتخصصات
<?php if (!empty($specialization_stats)): ?>
document.addEventListener('DOMContentLoaded', function() {
    const ctx2 = document.getElementById('specializationChart').getContext('2d');

    // بيانات التخصصات
    const specializationData = {
        labels: [
            <?php foreach ($specialization_stats as $stat): ?>
            '<?php echo addslashes($stat['specialization']); ?>',
            <?php endforeach; ?>
        ],
        datasets: [{
            data: [
                <?php foreach ($specialization_stats as $stat): ?>
                <?php echo $stat['count']; ?>,
                <?php endforeach; ?>
            ],
            backgroundColor: [
                <?php foreach ($specialization_stats as $index => $stat): ?>
                '<?php echo getSpecializationColor($index); ?>',
                <?php endforeach; ?>
            ],
            borderColor: '#fff',
            borderWidth: 3,
            hoverBorderWidth: 5,
            hoverBorderColor: '#fff'
        }]
    };

    // إعدادات الرسم البياني للتخصصات
    const config2 = {
        type: 'pie',
        data: specializationData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#fff',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: true,
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${label}: ${value} موظف (${percentage}%)`;
                        }
                    }
                }
            },
            animation: {
                animateRotate: true,
                animateScale: true,
                duration: 1800,
                easing: 'easeOutBounce'
            },
            elements: {
                arc: {
                    borderWidth: 3,
                    hoverBorderWidth: 5
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    };

    // إنشاء الرسم البياني للتخصصات
    const specializationChart = new Chart(ctx2, config2);

    // إضافة تأثيرات تفاعلية
    ctx2.canvas.addEventListener('mousemove', function(e) {
        const points = specializationChart.getElementsAtEventForMode(e, 'nearest', { intersect: true }, true);

        if (points.length) {
            ctx2.canvas.style.cursor = 'pointer';
        } else {
            ctx2.canvas.style.cursor = 'default';
        }
    });
});
<?php endif; ?>

// رسم بياني دائري للنوع (ذكر/أنثى)
<?php if ($total_employees > 0): ?>
document.addEventListener('DOMContentLoaded', function() {
    const ctx3 = document.getElementById('genderChart').getContext('2d');

    // بيانات النوع
    const genderData = {
        labels: ['ذكور', 'إناث'],
        datasets: [{
            data: [<?php echo $male_employees; ?>, <?php echo $female_employees; ?>],
            backgroundColor: ['#36A2EB', '#FF6384'],
            borderColor: '#fff',
            borderWidth: 3,
            hoverBorderWidth: 5,
            hoverBorderColor: '#fff'
        }]
    };

    // إعدادات الرسم البياني للنوع
    const config3 = {
        type: 'doughnut',
        data: genderData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#fff',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: true,
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${label}: ${value} موظف (${percentage}%)`;
                        }
                    }
                }
            },
            animation: {
                animateRotate: true,
                animateScale: true,
                duration: 1500,
                easing: 'easeInOutQuart'
            },
            cutout: '50%',
            elements: {
                arc: {
                    borderWidth: 3,
                    hoverBorderWidth: 5
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    };

    // إنشاء الرسم البياني للنوع
    const genderChart = new Chart(ctx3, config3);

    // إضافة تأثيرات تفاعلية
    ctx3.canvas.addEventListener('mousemove', function(e) {
        const points = genderChart.getElementsAtEventForMode(e, 'nearest', { intersect: true }, true);

        if (points.length) {
            ctx3.canvas.style.cursor = 'pointer';
        } else {
            ctx3.canvas.style.cursor = 'default';
        }
    });
});
<?php endif; ?>

// رسم بياني دائري للتجنيد
<?php if (!empty($military_stats)): ?>
document.addEventListener('DOMContentLoaded', function() {
    const ctx4 = document.getElementById('militaryChart').getContext('2d');

    // بيانات التجنيد
    const militaryData = {
        labels: [
            <?php foreach ($military_stats as $stat): ?>
            '<?php echo addslashes($stat['military_status']); ?>',
            <?php endforeach; ?>
        ],
        datasets: [{
            data: [
                <?php foreach ($military_stats as $stat): ?>
                <?php echo $stat['count']; ?>,
                <?php endforeach; ?>
            ],
            backgroundColor: [
                <?php foreach ($military_stats as $index => $stat): ?>
                '<?php echo getMilitaryColor($index); ?>',
                <?php endforeach; ?>
            ],
            borderColor: '#fff',
            borderWidth: 3,
            hoverBorderWidth: 5,
            hoverBorderColor: '#fff'
        }]
    };

    // إعدادات الرسم البياني للتجنيد
    const config4 = {
        type: 'doughnut',
        data: militaryData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#fff',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: true,
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${label}: ${value} موظف (${percentage}%)`;
                        }
                    }
                }
            },
            animation: {
                animateRotate: true,
                animateScale: true,
                duration: 1600,
                easing: 'easeOutElastic'
            },
            cutout: '55%',
            elements: {
                arc: {
                    borderWidth: 3,
                    hoverBorderWidth: 5
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    };

    // إنشاء الرسم البياني للتجنيد
    const militaryChart = new Chart(ctx4, config4);

    // إضافة تأثيرات تفاعلية
    ctx4.canvas.addEventListener('mousemove', function(e) {
        const points = militaryChart.getElementsAtEventForMode(e, 'nearest', { intersect: true }, true);

        if (points.length) {
            ctx4.canvas.style.cursor = 'pointer';
        } else {
            ctx4.canvas.style.cursor = 'default';
        }
    });
});
<?php endif; ?>

// رسم بياني دائري للخدمة العامة
<?php if (!empty($public_service_stats)): ?>
document.addEventListener('DOMContentLoaded', function() {
    const ctx5 = document.getElementById('publicServiceChart').getContext('2d');

    // بيانات الخدمة العامة
    const publicServiceData = {
        labels: [
            <?php foreach ($public_service_stats as $stat): ?>
            '<?php echo addslashes($stat['public_service_status']); ?>',
            <?php endforeach; ?>
        ],
        datasets: [{
            data: [
                <?php foreach ($public_service_stats as $stat): ?>
                <?php echo $stat['count']; ?>,
                <?php endforeach; ?>
            ],
            backgroundColor: [
                <?php foreach ($public_service_stats as $index => $stat): ?>
                '<?php echo getPublicServiceColor($index); ?>',
                <?php endforeach; ?>
            ],
            borderColor: '#fff',
            borderWidth: 3,
            hoverBorderWidth: 5,
            hoverBorderColor: '#fff'
        }]
    };

    // إعدادات الرسم البياني للخدمة العامة
    const config5 = {
        type: 'doughnut',
        data: publicServiceData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#fff',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: true,
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${label}: ${value} موظفة (${percentage}%)`;
                        }
                    }
                }
            },
            animation: {
                animateRotate: true,
                animateScale: true,
                duration: 1600,
                easing: 'easeOutElastic'
            },
            cutout: '55%',
            elements: {
                arc: {
                    borderWidth: 3,
                    hoverBorderWidth: 5
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    };

    // إنشاء الرسم البياني للخدمة العامة
    const publicServiceChart = new Chart(ctx5, config5);

    // إضافة تأثيرات تفاعلية
    ctx5.canvas.addEventListener('mousemove', function(e) {
        const points = publicServiceChart.getElementsAtEventForMode(e, 'nearest', { intersect: true }, true);

        if (points.length) {
            ctx5.canvas.style.cursor = 'pointer';
        } else {
            ctx5.canvas.style.cursor = 'default';
        }
    });
});
<?php endif; ?>
</script>

<style>
/* تحسينات إضافية للرسم البياني */
.chart-container {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 15px;
    padding: 20px;
    box-shadow: inset 0 2px 10px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.chart-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.qualification-legend {
    max-height: 200px;
    overflow-y: auto;
}

.specialization-legend {
    max-height: 150px;
    overflow-y: auto;
}

.gender-legend {
    max-height: 120px;
    overflow-y: auto;
}

.military-legend {
    max-height: 140px;
    overflow-y: auto;
}

.public-service-legend {
    max-height: 140px;
    overflow-y: auto;
}

.qualification-legend::-webkit-scrollbar,
.specialization-legend::-webkit-scrollbar,
.gender-legend::-webkit-scrollbar,
.military-legend::-webkit-scrollbar,
.public-service-legend::-webkit-scrollbar {
    width: 6px;
}

.qualification-legend::-webkit-scrollbar-track,
.specialization-legend::-webkit-scrollbar-track,
.gender-legend::-webkit-scrollbar-track,
.military-legend::-webkit-scrollbar-track,
.public-service-legend::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.qualification-legend::-webkit-scrollbar-thumb,
.specialization-legend::-webkit-scrollbar-thumb,
.gender-legend::-webkit-scrollbar-thumb,
.military-legend::-webkit-scrollbar-thumb,
.public-service-legend::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

.qualification-legend::-webkit-scrollbar-thumb:hover,
.specialization-legend::-webkit-scrollbar-thumb:hover,
.gender-legend::-webkit-scrollbar-thumb:hover,
.military-legend::-webkit-scrollbar-thumb:hover,
.public-service-legend::-webkit-scrollbar-thumb:hover {
    background: #555;
}

.legend-color {
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    transition: transform 0.2s ease;
}

.qualification-legend .d-flex:hover .legend-color,
.specialization-legend .d-flex:hover .legend-color,
.gender-legend .d-flex:hover .legend-color,
.military-legend .d-flex:hover .legend-color,
.public-service-legend .d-flex:hover .legend-color {
    transform: scale(1.2);
}

.qualification-legend .d-flex:hover,
.specialization-legend .d-flex:hover,
.gender-legend .d-flex:hover,
.military-legend .d-flex:hover,
.public-service-legend .d-flex:hover {
    background: rgba(0,0,0,0.1) !important;
    transform: translateX(5px);
    transition: all 0.3s ease;
}

/* تأثيرات متحركة للبطاقة */
.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
}

.card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* تحسين شكل الشارات */
.badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
    border-radius: 10px;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* تحسينات إضافية للرسوم البيانية */
canvas {
    position: relative;
    z-index: 2;
}

/* تأثيرات للأساطير */
.qualification-legend .d-flex,
.specialization-legend .d-flex,
.gender-legend .d-flex,
.military-legend .d-flex,
.public-service-legend .d-flex {
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
}

/* تحسين ألوان الشارات */
.bg-pink {
    background-color: #e91e63 !important;
    color: white !important;
}

/* تأثيرات الحركة للبطاقات */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeInUp 0.6s ease-out;
}

.card:nth-child(1) { animation-delay: 0.1s; }
.card:nth-child(2) { animation-delay: 0.2s; }
.card:nth-child(3) { animation-delay: 0.3s; }
.card:nth-child(4) { animation-delay: 0.4s; }
</style>

<?php include 'includes/footer.php'; ?>
