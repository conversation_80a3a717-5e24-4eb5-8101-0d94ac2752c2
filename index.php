<?php
/**
 * الصفحة الرئيسية - لوحة التحكم
 * Main Dashboard Page
 */

require_once 'includes/config.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
requireLogin();

$page_title = 'لوحة التحكم';
$db = getDatabase();

// إحصائيات سريعة
$total_employees = $db->count('employees');
$male_employees = $db->count('employees', 'gender = ?', ['ذكر']);
$female_employees = $db->count('employees', 'gender = ?', ['أنثى']);
$married_employees = $db->count('employees', 'marital_status = ?', ['متزوج']);
$disabled_employees = $db->count('employees', 'has_disability = ?', [1]);

// إحصائيات تحذيرات الرقم القومي
$expired_national_ids = $db->fetchColumn("
    SELECT COUNT(*) FROM employees
    WHERE national_id_expiry_date IS NOT NULL
    AND national_id_expiry_date < CURDATE()
");

$expiring_soon_national_ids = $db->fetchColumn("
    SELECT COUNT(*) FROM employees
    WHERE national_id_expiry_date IS NOT NULL
    AND national_id_expiry_date >= CURDATE()
    AND DATEDIFF(national_id_expiry_date, CURDATE()) <= 30
");

// إحصائيات تحذيرات شهادات التجنيد/الخدمة العامة
$expired_service_certificates = $db->fetchColumn("
    SELECT COUNT(*) FROM employees
    WHERE service_certificate_expiry_date IS NOT NULL
    AND service_certificate_expiry_date < CURDATE()
");

$expiring_soon_service_certificates = $db->fetchColumn("
    SELECT COUNT(*) FROM employees
    WHERE service_certificate_expiry_date IS NOT NULL
    AND service_certificate_expiry_date >= CURDATE()
    AND DATEDIFF(service_certificate_expiry_date, CURDATE()) <= 30
");

// الموظفون المضافون حديثاً
$recent_employees = $db->fetchAll(
    "SELECT * FROM employee_full_view ORDER BY created_at DESC LIMIT 5"
);

// إحصائيات المؤهلات
$qualification_stats = $db->fetchAll(
    "SELECT qualification, COUNT(*) as count 
     FROM employees 
     GROUP BY qualification 
     ORDER BY count DESC 
     LIMIT 5"
);

// إحصائيات التخصصات
$specialization_stats = $db->fetchAll(
    "SELECT specialization, COUNT(*) as count
     FROM employees
     WHERE specialization IS NOT NULL AND specialization != ''
     GROUP BY specialization
     ORDER BY count DESC
     LIMIT 5"
);

// إحصائيات التجنيد للذكور
$military_stats = $db->fetchAll(
    "SELECT military_status, COUNT(*) as count
     FROM employees
     WHERE gender = 'ذكر' AND military_status != 'غير محدد'
     GROUP BY military_status
     ORDER BY count DESC"
);

// إحصائيات الخدمة العامة للإناث
$public_service_stats = $db->fetchAll(
    "SELECT public_service_status, COUNT(*) as count
     FROM employees
     WHERE gender = 'أنثى' AND public_service_status != 'غير محدد'
     GROUP BY public_service_status
     ORDER BY count DESC"
);

include 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt me-2"></i>
        لوحة التحكم
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshPage()">
                <i class="fas fa-sync-alt"></i> تحديث
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="printPage()">
                <i class="fas fa-print"></i> طباعة
            </button>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="stats-number"><?php echo number_format($total_employees); ?></div>
                    <div class="stats-label">إجمالي الموظفين</div>
                </div>
                <div class="flex-shrink-0">
                    <i class="fas fa-users fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="stats-number"><?php echo number_format($male_employees); ?></div>
                    <div class="stats-label">الموظفين الذكور</div>
                </div>
                <div class="flex-shrink-0">
                    <i class="fas fa-male fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="stats-number"><?php echo number_format($female_employees); ?></div>
                    <div class="stats-label">الموظفات الإناث</div>
                </div>
                <div class="flex-shrink-0">
                    <i class="fas fa-female fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); color: #333;">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="stats-number"><?php echo number_format($married_employees); ?></div>
                    <div class="stats-label">المتزوجين</div>
                </div>
                <div class="flex-shrink-0">
                    <i class="fas fa-heart fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- تحذيرات الرقم القومي -->
<?php if ($expired_national_ids > 0 || $expiring_soon_national_ids > 0): ?>
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-warning border-start border-warning border-4" role="alert">
            <div class="d-flex align-items-center">
                <i class="fas fa-exclamation-triangle fa-2x text-warning me-3"></i>
                <div class="flex-grow-1">
                    <h5 class="alert-heading mb-2">تحذيرات الرقم القومي</h5>
                    <div class="row">
                        <?php if ($expired_national_ids > 0): ?>
                        <div class="col-md-6">
                            <span class="badge bg-danger me-2"><?php echo $expired_national_ids; ?></span>
                            موظف لديه رقم قومي منتهي الصلاحية
                        </div>
                        <?php endif; ?>
                        <?php if ($expiring_soon_national_ids > 0): ?>
                        <div class="col-md-6">
                            <span class="badge bg-warning text-dark me-2"><?php echo $expiring_soon_national_ids; ?></span>
                            موظف ينتهي رقمه القومي خلال 30 يوم
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <div>
                    <a href="pages/national_id_expiry_alerts.php" class="btn btn-warning">
                        <i class="fas fa-eye me-1"></i>عرض التفاصيل
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- تحذيرات شهادات التجنيد/الخدمة العامة -->
<?php if ($expired_service_certificates > 0 || $expiring_soon_service_certificates > 0): ?>
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-info border-start border-info border-4" role="alert">
            <div class="d-flex align-items-center">
                <i class="fas fa-certificate fa-2x text-info me-3"></i>
                <div class="flex-grow-1">
                    <h5 class="alert-heading mb-2">تحذيرات شهادات التجنيد/الخدمة العامة</h5>
                    <div class="row">
                        <?php if ($expired_service_certificates > 0): ?>
                        <div class="col-md-6">
                            <span class="badge bg-danger me-2"><?php echo $expired_service_certificates; ?></span>
                            شهادة منتهية الصلاحية
                        </div>
                        <?php endif; ?>
                        <?php if ($expiring_soon_service_certificates > 0): ?>
                        <div class="col-md-6">
                            <span class="badge bg-warning text-dark me-2"><?php echo $expiring_soon_service_certificates; ?></span>
                            شهادة تنتهي خلال 30 يوم
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <div>
                    <a href="pages/service_certificate_expiry_alerts.php" class="btn btn-info">
                        <i class="fas fa-eye me-1"></i>عرض التفاصيل
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="row">
    <!-- الموظفون المضافون حديثاً -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-plus me-2"></i>
                    الموظفون المضافون حديثاً
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($recent_employees)): ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-users fa-3x mb-3"></i>
                        <p>لا توجد بيانات موظفين</p>
                        <?php if (hasPermission('hr_employee')): ?>
                            <a href="pages/add_employee.php" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>إضافة موظف جديد
                            </a>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>كود الموظف</th>
                                    <th>الاسم</th>
                                    <th>المؤهل</th>
                                    <th>التخصص</th>
                                    <th>تاريخ الإضافة</th>
                                    <th>العمليات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_employees as $employee): ?>
                                <tr>
                                    <td>
                                        <span class="badge bg-primary"><?php echo $employee['employee_code']; ?></span>
                                    </td>
                                    <td>
                                        <strong><?php echo $employee['full_name']; ?></strong>
                                    </td>
                                    <td><?php echo $employee['qualification']; ?></td>
                                    <td><?php echo $employee['specialization'] ?: '-'; ?></td>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo formatDateTime($employee['created_at'], 'Y-m-d H:i'); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <a href="pages/view_employee.php?id=<?php echo $employee['id']; ?>" 
                                           class="btn btn-sm btn-outline-primary" 
                                           data-bs-toggle="tooltip" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php if (hasPermission('hr_employee')): ?>
                                        <a href="pages/edit_employee.php?id=<?php echo $employee['id']; ?>" 
                                           class="btn btn-sm btn-outline-warning" 
                                           data-bs-toggle="tooltip" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center">
                        <a href="pages/view_employees.php" class="btn btn-outline-primary">
                            عرض جميع الموظفين <i class="fas fa-arrow-left ms-2"></i>
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- إحصائيات جانبية -->
    <div class="col-lg-4">
        <!-- إحصائيات المؤهلات -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-graduation-cap me-2"></i>
                    توزيع المؤهلات
                </h6>
            </div>
            <div class="card-body">
                <?php if (empty($qualification_stats)): ?>
                    <p class="text-muted text-center">لا توجد بيانات</p>
                <?php else: ?>
                    <?php foreach ($qualification_stats as $stat): ?>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span><?php echo $stat['qualification']; ?></span>
                        <span class="badge bg-primary"><?php echo $stat['count']; ?></span>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- إحصائيات التخصصات -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-briefcase me-2"></i>
                    أهم التخصصات
                </h6>
            </div>
            <div class="card-body">
                <?php if (empty($specialization_stats)): ?>
                    <p class="text-muted text-center">لا توجد بيانات</p>
                <?php else: ?>
                    <?php foreach ($specialization_stats as $stat): ?>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span><?php echo $stat['specialization']; ?></span>
                        <span class="badge bg-success"><?php echo $stat['count']; ?></span>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- إحصائيات التجنيد -->
        <?php if (!empty($military_stats)): ?>
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-user-shield me-2"></i>
                    إحصائيات التجنيد (الذكور)
                </h6>
            </div>
            <div class="card-body">
                <?php foreach ($military_stats as $stat): ?>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span><?php echo $stat['military_status']; ?></span>
                    <span class="badge bg-info"><?php echo $stat['count']; ?></span>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- إحصائيات الخدمة العامة -->
        <?php if (!empty($public_service_stats)): ?>
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <h6 class="card-title mb-0">
                    <i class="fas fa-hands-helping me-2"></i>
                    إحصائيات الخدمة العامة (الإناث)
                </h6>
            </div>
            <div class="card-body">
                <?php foreach ($public_service_stats as $stat): ?>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span><?php echo $stat['public_service_status']; ?></span>
                    <span class="badge bg-warning text-dark"><?php echo $stat['count']; ?></span>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- روابط سريعة -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    روابط سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <?php if (hasPermission('hr_employee')): ?>
                    <a href="pages/add_employee.php" class="btn btn-primary btn-sm">
                        <i class="fas fa-user-plus me-2"></i>إضافة موظف جديد
                    </a>
                    <?php endif; ?>
                    
                    <a href="pages/search.php" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-search me-2"></i>البحث المتقدم
                    </a>
                    
                    <a href="pages/reports.php" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-chart-bar me-2"></i>التقارير
                    </a>
                    
                    <?php if (hasPermission('admin')): ?>
                    <a href="pages/backup.php" class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-database me-2"></i>النسخ الاحتياطي
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
