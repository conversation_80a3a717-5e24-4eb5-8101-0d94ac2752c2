<?php
// تفعيل عرض جميع الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<h1>اختبار PHP الأساسي</h1>";
echo "<p>إذا رأيت هذه الرسالة، فإن PHP يعمل بشكل صحيح.</p>";

echo "<h2>معلومات PHP:</h2>";
echo "إصدار PHP: " . phpversion() . "<br>";
echo "نظام التشغيل: " . PHP_OS . "<br>";
echo "الوقت الحالي: " . date('Y-m-d H:i:s') . "<br>";

echo "<h2>اختبار الملفات:</h2>";
$files = [
    'config/database.php',
    'includes/functions.php', 
    'includes/header.php',
    'index.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "✅ $file موجود<br>";
    } else {
        echo "❌ $file مفقود<br>";
    }
}

echo "<h2>اختبار تضمين الملفات:</h2>";
try {
    if (file_exists('config/database.php')) {
        echo "محاولة تضمين config/database.php...<br>";
        include_once 'config/database.php';
        echo "✅ تم تضمين config/database.php بنجاح<br>";
    }
} catch (Exception $e) {
    echo "❌ خطأ في config/database.php: " . $e->getMessage() . "<br>";
} catch (Error $e) {
    echo "❌ خطأ فادح في config/database.php: " . $e->getMessage() . "<br>";
}

try {
    if (file_exists('includes/functions.php')) {
        echo "محاولة تضمين includes/functions.php...<br>";
        include_once 'includes/functions.php';
        echo "✅ تم تضمين includes/functions.php بنجاح<br>";
    }
} catch (Exception $e) {
    echo "❌ خطأ في includes/functions.php: " . $e->getMessage() . "<br>";
} catch (Error $e) {
    echo "❌ خطأ فادح في includes/functions.php: " . $e->getMessage() . "<br>";
}

echo "<h2>اختبار MySQL:</h2>";
try {
    $pdo = new PDO("mysql:host=localhost", "root", "");
    echo "✅ اتصال MySQL نجح<br>";
} catch (PDOException $e) {
    echo "❌ فشل اتصال MySQL: " . $e->getMessage() . "<br>";
}

phpinfo();
?>
