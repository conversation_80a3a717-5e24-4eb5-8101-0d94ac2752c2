-- تحديث قاعدة البيانات لإضافة بيانات التجنيد والخدمة العامة
-- Database Update Script for Military and Public Service Data

USE employee_management;

-- إضا<PERSON>ة حقول التجنيد والخدمة العامة إلى جدول الموظفين
ALTER TABLE employees 
ADD COLUMN military_status ENUM('قام بالتجنيد', 'إعفاء مؤقت', 'إعفاء نهائي', 'تأجيل التجنيد', 'غير محدد') DEFAULT 'غير محدد' AFTER disability_type,
ADD COLUMN military_status_date DATE AFTER military_status,
ADD COLUMN military_notes TEXT AFTER military_status_date,
ADD COLUMN public_service_status ENUM('تأدية الخدمة', 'لم تؤدى الخدمة', 'غير محدد') DEFAULT 'غير محدد' AFTER military_notes,
ADD COLUMN public_service_date DATE AFTER public_service_status,
ADD COLUMN public_service_notes TEXT AFTER public_service_date;

-- إضافة فهارس للبحث السريع
ALTER TABLE employees 
ADD INDEX idx_military_status (military_status),
ADD INDEX idx_public_service_status (public_service_status);

-- تحديث البيانات الموجودة (اختياري)
-- يمكن تشغيل هذه الاستعلامات لتحديث البيانات الموجودة

-- مثال: تحديث بيانات موظف معين
-- UPDATE employees 
-- SET military_status = 'قام بالتجنيد', 
--     military_status_date = '2020-01-15',
--     military_notes = 'أدى الخدمة العسكرية الإلزامية'
-- WHERE id = 1 AND gender = 'ذكر';

-- مثال: تحديث بيانات موظفة معينة
-- UPDATE employees 
-- SET public_service_status = 'تأدية الخدمة', 
--     public_service_date = '2021-03-10',
--     public_service_notes = 'أدت الخدمة العامة في وزارة الصحة'
-- WHERE id = 2 AND gender = 'أنثى';

-- إعادة إنشاء الفيو لتشمل الحقول الجديدة
DROP VIEW IF EXISTS employee_full_view;

CREATE VIEW employee_full_view AS
SELECT 
    e.*,
    CONCAT(e.first_name, ' ', e.father_name, ' ', e.grandfather_name, ' ', e.family_name) as full_name,
    u1.full_name as created_by_name,
    u2.full_name as updated_by_name
FROM employees e
LEFT JOIN users u1 ON e.created_by = u1.id
LEFT JOIN users u2 ON e.updated_by = u2.id;

-- إضافة بيانات تجريبية جديدة (اختياري)
INSERT INTO employees (
    employee_code, first_name, father_name, grandfather_name, family_name,
    national_id, phone, governorate, city, gender, marital_status,
    military_status, military_status_date, military_notes,
    qualification, specialization, created_by
) VALUES 
(
    'EMP005', 'خالد', 'أحمد', 'محمد', 'علي',
    '29712345678903', '01234567891', 'القاهرة', 'المعادي', 'ذكر', 'أعزب',
    'تأجيل التجنيد', '2023-09-15', 'تأجيل التجنيد للدراسة الجامعية',
    'بكالوريوس علوم', 'رياضيات', 1
);

INSERT INTO employees (
    employee_code, first_name, father_name, grandfather_name, family_name,
    national_id, phone, governorate, city, gender, marital_status,
    public_service_status, public_service_date, public_service_notes,
    qualification, specialization, created_by
) VALUES 
(
    'EMP006', 'نورا', 'محمد', 'سعد', 'حسن',
    '29812345678904', '01234567892', 'الجيزة', 'الهرم', 'أنثى', 'أعزب',
    'لم تؤدى الخدمة', NULL, 'لم تؤدي الخدمة العامة بعد التخرج',
    'بكالوريوس طب', 'طب عام', 1
);

-- تحديث إحصائيات قاعدة البيانات
ANALYZE TABLE employees;

-- التحقق من التحديث
SELECT 
    'إجمالي الموظفين' as البيان,
    COUNT(*) as العدد
FROM employees
UNION ALL
SELECT 
    'الذكور الذين أدوا التجنيد' as البيان,
    COUNT(*) as العدد
FROM employees 
WHERE gender = 'ذكر' AND military_status = 'قام بالتجنيد'
UNION ALL
SELECT 
    'الإناث اللواتي أدين الخدمة العامة' as البيان,
    COUNT(*) as العدد
FROM employees 
WHERE gender = 'أنثى' AND public_service_status = 'تأدية الخدمة'
UNION ALL
SELECT 
    'الذكور المعفيين من التجنيد' as البيان,
    COUNT(*) as العدد
FROM employees 
WHERE gender = 'ذكر' AND military_status IN ('إعفاء مؤقت', 'إعفاء نهائي')
UNION ALL
SELECT 
    'الإناث اللواتي لم يؤدين الخدمة العامة' as البيان,
    COUNT(*) as العدد
FROM employees 
WHERE gender = 'أنثى' AND public_service_status = 'لم تؤدى الخدمة';

-- رسالة نجاح التحديث
SELECT 'تم تحديث قاعدة البيانات بنجاح!' as الحالة;
