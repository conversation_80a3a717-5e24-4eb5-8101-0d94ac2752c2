# نظام إدارة بيانات الموظفين
## Employee Management System

نظام شامل لإدارة بيانات الموظفين مطور بـ PHP و MySQL مع دعم كامل للغة العربية.

## المميزات الرئيسية

### 🏢 إدارة شاملة للموظفين
- إضافة وتعديل وحذف بيانات الموظفين
- الاسم الخماسي الكامل
- الرقم القومي والتأميني
- بيانات الاتصال والعنوان
- المؤهلات العلمية والتخصص
- الحالة الاجتماعية ومعلومات الإعاقة
- بيانات التجنيد للذكور (حالة التجنيد، تاريخ المعاملة، ملاحظات)
- بيانات الخدمة العامة للإناث (حالة الخدمة، تاريخ المعاملة، ملاحظات)

### 🔍 البحث والتصفية المتقدمة
- البحث بالاسم أو الكود أو الرقم القومي
- تصفية حسب النوع والمؤهل والحالة الاجتماعية
- تصفية حسب المحافظة والتخصص
- تصفية حسب حالة التجنيد والخدمة العامة
- ترقيم الصفحات

### 📊 التقارير والإحصائيات
- إحصائيات سريعة في لوحة التحكم
- توزيع الموظفين حسب المؤهلات
- إحصائيات النوع والحالة الاجتماعية
- إحصائيات التجنيد والخدمة العامة
- تقارير قابلة للطباعة

### 🔐 نظام الصلاحيات
- **مدير النظام (Admin)**: صلاحيات كاملة
- **مدير الموارد البشرية (HR Manager)**: إدارة الموظفين والتقارير
- **موظف الموارد البشرية (HR Employee)**: إضافة وتعديل الموظفين
- **مستخدم عادي (Viewer)**: عرض البيانات فقط

### 🛡️ الأمان والتدقيق
- تشفير كلمات المرور
- سجل تدقيق لجميع العمليات
- التحقق من صحة البيانات
- حماية من SQL Injection و XSS

### 🌐 واجهة مستخدم متطورة
- تصميم متجاوب يعمل على جميع الأجهزة
- دعم كامل للغة العربية (RTL)
- واجهة حديثة باستخدام Bootstrap 5
- تأثيرات بصرية جذابة

## متطلبات النظام

- **خادم ويب**: Apache أو Nginx
- **PHP**: الإصدار 7.4 أو أحدث
- **قاعدة البيانات**: MySQL 5.7 أو أحدث / MariaDB 10.2 أو أحدث
- **الذاكرة**: 512 MB RAM كحد أدنى
- **مساحة القرص**: 100 MB كحد أدنى

## التثبيت والإعداد

### 1. تحميل الملفات
```bash
# نسخ الملفات إلى مجلد الخادم
cp -r employee_management/ /var/www/html/
# أو في XAMPP
cp -r employee_management/ C:/xampp/htdocs/
```

### 2. إعداد قاعدة البيانات
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE employee_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- استيراد الهيكل والبيانات
mysql -u root -p employee_management < database.sql
```

### 3. تكوين الاتصال
قم بتعديل ملف `config/database.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'employee_management');
define('DB_USER', 'root');
define('DB_PASS', 'your_password');
```

### 4. إعداد الصلاحيات
```bash
# إعطاء صلاحيات الكتابة لمجلد الرفع
chmod 755 uploads/
chown www-data:www-data uploads/
```

### 5. الوصول للنظام
افتح المتصفح وانتقل إلى:
```
http://localhost/employee_management/
```

## بيانات الدخول الافتراضية

- **اسم المستخدم**: admin
- **كلمة المرور**: password

⚠️ **مهم**: قم بتغيير كلمة المرور فور تسجيل الدخول الأول!

## هيكل المشروع

```
employee_management/
├── config/
│   └── database.php          # إعدادات قاعدة البيانات
├── includes/
│   ├── header.php           # رأس الصفحة
│   ├── footer.php           # تذييل الصفحة
│   └── functions.php        # الدوال المساعدة
├── pages/
│   ├── add_employee.php     # إضافة موظف
│   ├── view_employees.php   # عرض الموظفين
│   ├── view_employee.php    # تفاصيل موظف
│   ├── edit_employee.php    # تعديل موظف
│   ├── delete_employee.php  # حذف موظف
│   ├── search.php           # البحث المتقدم
│   └── reports.php          # التقارير
├── assets/
│   └── style.css            # التنسيقات المخصصة
├── uploads/                 # ملفات المرفوعة
├── index.php               # لوحة التحكم
├── login.php               # تسجيل الدخول
├── logout.php              # تسجيل الخروج
├── database.sql            # هيكل قاعدة البيانات
└── README.md               # هذا الملف
```

## الاستخدام

### إضافة موظف جديد
1. انتقل إلى "إضافة موظف جديد"
2. املأ البيانات المطلوبة (الحقول المميزة بـ * مطلوبة)
3. اضغط "حفظ البيانات"

### البحث عن موظف
1. انتقل إلى "عرض الموظفين"
2. استخدم مربع البحث أو الفلاتر
3. اضغط على أيقونة العين لعرض التفاصيل

### تعديل بيانات موظف
1. ابحث عن الموظف
2. اضغط على أيقونة التعديل
3. قم بالتعديلات المطلوبة
4. احفظ التغييرات

## التخصيص والتطوير

### إضافة حقول جديدة
1. قم بتعديل جدول `employees` في قاعدة البيانات
2. أضف الحقول في نماذج الإدخال
3. حدث دوال الحفظ والعرض

### تخصيص التصميم
- عدل ملف `assets/style.css` للتنسيقات
- استخدم متغيرات CSS المعرفة في `:root`
- يمكن تغيير الألوان والخطوط بسهولة

### إضافة تقارير جديدة
1. أنشئ ملف PHP جديد في مجلد `pages/`
2. استخدم الدوال الموجودة في `includes/functions.php`
3. أضف الرابط في القائمة الجانبية

## الأمان

### حماية الملفات
```apache
# .htaccess في مجلد config/
<Files "*.php">
    Order allow,deny
    Deny from all
</Files>
```

### النسخ الاحتياطي
```bash
# نسخ احتياطي لقاعدة البيانات
mysqldump -u root -p employee_management > backup_$(date +%Y%m%d).sql

# نسخ احتياطي للملفات
tar -czf backup_files_$(date +%Y%m%d).tar.gz employee_management/
```

## استكشاف الأخطاء

### مشاكل شائعة

**خطأ في الاتصال بقاعدة البيانات**
- تأكد من صحة بيانات الاتصال في `config/database.php`
- تأكد من تشغيل خدمة MySQL

**صفحة فارغة أو خطأ 500**
- فعل عرض الأخطاء: `error_reporting(E_ALL)`
- تحقق من ملفات سجل الأخطاء

**مشاكل في الترميز العربي**
- تأكد من استخدام `utf8mb4` في قاعدة البيانات
- تأكد من وجود `<meta charset="UTF-8">` في HTML

## الدعم والمساهمة

### الإبلاغ عن مشاكل
- استخدم نظام Issues في GitHub
- قدم وصفاً مفصلاً للمشكلة
- أرفق لقطات شاشة إن أمكن

### المساهمة في التطوير
1. Fork المشروع
2. أنشئ branch جديد للميزة
3. قم بالتعديلات المطلوبة
4. أرسل Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الشكر والتقدير

- Bootstrap لإطار العمل الأمامي
- Font Awesome للأيقونات
- Google Fonts للخطوط العربية

---

**تم التطوير بـ ❤️ لخدمة المجتمع العربي**
