# دليل تحديث النظام - إضافة بيانات التجنيد والخدمة العامة
## System Update Guide - Military and Public Service Data

## 🆕 الميزات الجديدة المضافة

### 📋 بيانات التجنيد للذكور:
- **حالة التجنيد**: قام بالتجنيد، إعفاء مؤقت، إعفاء نهائي، تأجيل التجنيد
- **تاريخ المعاملة**: تاريخ إجراء المعاملة
- **ملاحظات التجنيد**: تفاصيل إضافية حول حالة التجنيد

### 👩‍💼 بيانات الخدمة العامة للإناث:
- **حالة الخدمة العامة**: تأدية الخدمة، لم تؤدى الخدمة
- **تاريخ المعاملة**: تاريخ إجراء المعاملة
- **ملاحظات الخدمة العامة**: تفاصيل إضافية حول الخدمة

### 🔍 تحسينات البحث والتصفية:
- فلاتر جديدة لحالة التجنيد
- فلاتر جديدة لحالة الخدمة العامة
- تفعيل/إلغاء تفعيل الفلاتر حسب النوع المحدد

### 📊 إحصائيات جديدة:
- إحصائيات التجنيد للذكور في لوحة التحكم
- إحصائيات الخدمة العامة للإناث في لوحة التحكم

## 🔄 خطوات التحديث

### للمستخدمين الجدد:
1. استخدم ملف `database.sql` الجديد المحدث
2. لا حاجة لخطوات إضافية

### للمستخدمين الحاليين:
1. **عمل نسخة احتياطية من قاعدة البيانات**:
   ```sql
   mysqldump -u root -p employee_management > backup_before_update.sql
   ```

2. **تشغيل ملف التحديث**:
   ```sql
   mysql -u root -p employee_management < update_database.sql
   ```

3. **التحقق من نجاح التحديث**:
   ```sql
   DESCRIBE employees;
   ```

### التحديث اليدوي (إذا لزم الأمر):
```sql
-- إضافة الحقول الجديدة
ALTER TABLE employees 
ADD COLUMN military_status ENUM('قام بالتجنيد', 'إعفاء مؤقت', 'إعفاء نهائي', 'تأجيل التجنيد', 'غير محدد') DEFAULT 'غير محدد' AFTER disability_type,
ADD COLUMN military_status_date DATE AFTER military_status,
ADD COLUMN military_notes TEXT AFTER military_status_date,
ADD COLUMN public_service_status ENUM('تأدية الخدمة', 'لم تؤدى الخدمة', 'غير محدد') DEFAULT 'غير محدد' AFTER military_notes,
ADD COLUMN public_service_date DATE AFTER public_service_status,
ADD COLUMN public_service_notes TEXT AFTER public_service_date;

-- إضافة الفهارس
ALTER TABLE employees 
ADD INDEX idx_military_status (military_status),
ADD INDEX idx_public_service_status (public_service_status);
```

## 📁 الملفات المحدثة

### ملفات قاعدة البيانات:
- ✅ `database.sql` - محدث بالحقول الجديدة
- ✅ `update_database.sql` - ملف تحديث للمستخدمين الحاليين

### ملفات PHP:
- ✅ `pages/add_employee.php` - إضافة حقول التجنيد والخدمة العامة
- ✅ `pages/view_employees.php` - فلاتر وعرض البيانات الجديدة
- ✅ `pages/view_employee.php` - عرض تفاصيل التجنيد والخدمة العامة
- ✅ `index.php` - إحصائيات التجنيد والخدمة العامة

### ملفات التوثيق:
- ✅ `README.md` - محدث بالميزات الجديدة
- ✅ `PROJECT_SUMMARY.md` - محدث بالإنجازات الجديدة
- ✅ `UPDATE_GUIDE.md` - هذا الملف

## 🎯 كيفية استخدام الميزات الجديدة

### 1. إضافة موظف جديد:
- اختر النوع (ذكر/أنثى)
- ستظهر الحقول المناسبة تلقائياً:
  - للذكور: بيانات التجنيد
  - للإناث: بيانات الخدمة العامة

### 2. البحث والتصفية:
- استخدم فلاتر "حالة التجنيد" للذكور
- استخدم فلاتر "الخدمة العامة" للإناث
- الفلاتر تتفعل/تلغى تلقائياً حسب النوع المحدد

### 3. عرض التقارير:
- راجع لوحة التحكم للإحصائيات الجديدة
- إحصائيات التجنيد تظهر للذكور فقط
- إحصائيات الخدمة العامة تظهر للإناث فقط

## 🔧 استكشاف الأخطاء

### خطأ في إضافة الحقول:
```
Error: Duplicate column name 'military_status'
```
**الحل**: الحقول موجودة بالفعل، لا حاجة للتحديث

### خطأ في الترميز:
```
Error: Incorrect string value
```
**الحل**: تأكد من استخدام `utf8mb4` في قاعدة البيانات

### عدم ظهور الحقول الجديدة:
1. تحقق من تحديث قاعدة البيانات
2. امسح cache المتصفح
3. تأكد من رفع الملفات المحدثة

## 📊 إحصائيات التحديث

### الحقول المضافة:
- **6 حقول جديدة** في جدول employees
- **2 فهرس جديد** لتحسين الأداء
- **4 بيانات تجريبية جديدة**

### الملفات المحدثة:
- **4 ملفات PHP** محدثة
- **3 ملفات توثيق** محدثة
- **2 ملف SQL** جديد

### الميزات الجديدة:
- **فلاتر ذكية** تتغير حسب النوع
- **إحصائيات تفاعلية** في لوحة التحكم
- **واجهة محسنة** للبيانات الجديدة

## ✅ التحقق من نجاح التحديث

### 1. فحص قاعدة البيانات:
```sql
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'employees' 
AND COLUMN_NAME IN ('military_status', 'public_service_status');
```

### 2. فحص الواجهة:
- افتح صفحة إضافة موظف
- اختر "ذكر" - يجب أن تظهر حقول التجنيد
- اختر "أنثى" - يجب أن تظهر حقول الخدمة العامة

### 3. فحص الفلاتر:
- افتح صفحة عرض الموظفين
- جرب فلاتر التجنيد والخدمة العامة
- تأكد من عمل الفلاتر الذكية

### 4. فحص الإحصائيات:
- افتح لوحة التحكم
- تأكد من ظهور إحصائيات التجنيد والخدمة العامة

## 🎉 مبروك!

تم تحديث النظام بنجاح! الآن يمكنك:
- ✅ إدارة بيانات التجنيد للذكور
- ✅ إدارة بيانات الخدمة العامة للإناث
- ✅ استخدام فلاتر البحث المتقدمة
- ✅ مراجعة الإحصائيات الشاملة

---

**ملاحظة**: في حالة مواجهة أي مشاكل، يرجى الرجوع للنسخة الاحتياطية واتباع خطوات التحديث مرة أخرى.
