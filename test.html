<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الخادم</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار الخادم</h1>
        
        <h2>إذا رأيت هذه الصفحة:</h2>
        <p class="success">✅ Apache يعمل بشكل صحيح</p>
        <p class="success">✅ يمكن الوصول للملفات</p>
        
        <h2>المشكلة المحتملة:</h2>
        <p class="error">❌ مشكلة في ملفات PHP</p>
        
        <h2>الحلول المقترحة:</h2>
        <ol>
            <li><strong>تحقق من سجل أخطاء Apache:</strong>
                <ul>
                    <li>في XAMPP: <code>C:\xampp\apache\logs\error.log</code></li>
                    <li>ابحث عن أخطاء حديثة</li>
                </ul>
            </li>
            
            <li><strong>تحقق من إعدادات PHP:</strong>
                <ul>
                    <li>افتح <code>C:\xampp\php\php.ini</code></li>
                    <li>تأكد من: <code>display_errors = On</code></li>
                    <li>تأكد من: <code>error_reporting = E_ALL</code></li>
                </ul>
            </li>
            
            <li><strong>أعد تشغيل Apache:</strong>
                <ul>
                    <li>من لوحة تحكم XAMPP</li>
                    <li>أوقف Apache ثم شغله مرة أخرى</li>
                </ul>
            </li>
            
            <li><strong>تحقق من مسار المشروع:</strong>
                <ul>
                    <li>تأكد أن المشروع في: <code>C:\xampp\htdocs\</code></li>
                    <li>الرابط الصحيح: <code>http://localhost/اسم_المجلد/</code></li>
                </ul>
            </li>
        </ol>
        
        <h2>اختبارات إضافية:</h2>
        <p>جرب هذه الروابط:</p>
        <ul>
            <li><a href="debug.php">debug.php</a> - اختبار PHP</li>
            <li><a href="test_connection.php">test_connection.php</a> - اختبار قاعدة البيانات</li>
            <li><a href="http://localhost/phpmyadmin">phpMyAdmin</a> - إدارة قاعدة البيانات</li>
        </ul>
        
        <h2>معلومات مفيدة:</h2>
        <p><strong>خادم Apache:</strong> Apache/2.4.58 (Win64)</p>
        <p><strong>إصدار PHP:</strong> PHP/8.2.12</p>
        <p><strong>نظام التشغيل:</strong> Windows</p>
        
        <div style="background: #fffacd; padding: 10px; border-radius: 5px; margin-top: 20px;">
            <strong>ملاحظة:</strong> إذا لم تظهر هذه الصفحة، فالمشكلة في Apache نفسه.
        </div>
    </div>
</body>
</html>
