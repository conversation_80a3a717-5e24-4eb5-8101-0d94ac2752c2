<?php
/**
 * صفحة عرض الموظفين
 * View Employees Page
 */

require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
requireLogin();

$page_title = 'عرض الموظفين';
$db = getDatabase();

// معاملات البحث والتصفية
$search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
$filter_gender = isset($_GET['gender']) ? sanitizeInput($_GET['gender']) : '';
$filter_qualification = isset($_GET['qualification']) ? sanitizeInput($_GET['qualification']) : '';
$filter_marital_status = isset($_GET['marital_status']) ? sanitizeInput($_GET['marital_status']) : '';
$filter_governorate = isset($_GET['governorate']) ? sanitizeInput($_GET['governorate']) : '';
$filter_military_status = isset($_GET['military_status']) ? sanitizeInput($_GET['military_status']) : '';
$filter_public_service_status = isset($_GET['public_service_status']) ? sanitizeInput($_GET['public_service_status']) : '';

// إعداد الاستعلام
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(CONCAT(first_name, ' ', father_name, ' ', grandfather_name, ' ', family_name) LIKE ? 
                          OR employee_code LIKE ? 
                          OR national_id LIKE ? 
                          OR phone LIKE ? 
                          OR email LIKE ?)";
    $search_param = '%' . $search . '%';
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param, $search_param]);
}

if (!empty($filter_gender)) {
    $where_conditions[] = "gender = ?";
    $params[] = $filter_gender;
}

if (!empty($filter_qualification)) {
    $where_conditions[] = "qualification = ?";
    $params[] = $filter_qualification;
}

if (!empty($filter_marital_status)) {
    $where_conditions[] = "marital_status = ?";
    $params[] = $filter_marital_status;
}

if (!empty($filter_governorate)) {
    $where_conditions[] = "governorate = ?";
    $params[] = $filter_governorate;
}

if (!empty($filter_military_status)) {
    $where_conditions[] = "military_status = ?";
    $params[] = $filter_military_status;
}

if (!empty($filter_public_service_status)) {
    $where_conditions[] = "public_service_status = ?";
    $params[] = $filter_public_service_status;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// الترقيم
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

// عد إجمالي السجلات
$total_sql = "SELECT COUNT(*) as total FROM employee_full_view " . $where_clause;
$total_result = $db->fetch($total_sql, $params);
$total_records = $total_result['total'];
$total_pages = ceil($total_records / $per_page);

// جلب البيانات
$sql = "SELECT * FROM employee_full_view " . $where_clause . " ORDER BY created_at DESC LIMIT ? OFFSET ?";
$params[] = $per_page;
$params[] = $offset;
$employees = $db->fetchAll($sql, $params);

// إحصائيات سريعة للتصفية
$gender_stats = $db->fetchAll("SELECT gender, COUNT(*) as count FROM employees GROUP BY gender");
$qualification_stats = $db->fetchAll("SELECT qualification, COUNT(*) as count FROM employees GROUP BY qualification ORDER BY count DESC LIMIT 10");

include '../includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-users me-2"></i>
        عرض الموظفين
        <span class="badge bg-primary"><?php echo number_format($total_records); ?></span>
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <?php if (hasPermission('hr_employee')): ?>
        <a href="add_employee.php" class="btn btn-primary me-2">
            <i class="fas fa-user-plus me-2"></i>إضافة موظف جديد
        </a>
        <?php endif; ?>
        <div class="btn-group me-2">
            <button type="button" class="btn btn-outline-secondary" onclick="printPage()">
                <i class="fas fa-print"></i>
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="exportToExcel('employeesTable', 'employees')">
                <i class="fas fa-file-excel"></i>
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="refreshPage()">
                <i class="fas fa-sync-alt"></i>
            </button>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="fas fa-filter me-2"></i>
            البحث والتصفية
        </h6>
    </div>
    <div class="card-body">
        <form method="GET" action="">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label for="search" class="form-label">البحث العام</label>
                    <input type="text" 
                           class="form-control" 
                           id="search" 
                           name="search" 
                           value="<?php echo htmlspecialchars($search); ?>"
                           placeholder="الاسم، الكود، الرقم القومي...">
                </div>
                
                <div class="col-md-2 mb-3">
                    <label for="gender" class="form-label">النوع</label>
                    <select class="form-select" id="gender" name="gender">
                        <option value="">الكل</option>
                        <option value="ذكر" <?php echo $filter_gender == 'ذكر' ? 'selected' : ''; ?>>ذكر</option>
                        <option value="أنثى" <?php echo $filter_gender == 'أنثى' ? 'selected' : ''; ?>>أنثى</option>
                    </select>
                </div>
                
                <div class="col-md-2 mb-3">
                    <label for="qualification" class="form-label">المؤهل</label>
                    <select class="form-select" id="qualification" name="qualification">
                        <option value="">الكل</option>
                        <?php foreach (getQualifications() as $qual): ?>
                            <option value="<?php echo $qual; ?>" <?php echo $filter_qualification == $qual ? 'selected' : ''; ?>>
                                <?php echo $qual; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-2 mb-3">
                    <label for="marital_status" class="form-label">الحالة الاجتماعية</label>
                    <select class="form-select" id="marital_status" name="marital_status">
                        <option value="">الكل</option>
                        <option value="أعزب" <?php echo $filter_marital_status == 'أعزب' ? 'selected' : ''; ?>>أعزب</option>
                        <option value="متزوج" <?php echo $filter_marital_status == 'متزوج' ? 'selected' : ''; ?>>متزوج</option>
                        <option value="أرمل" <?php echo $filter_marital_status == 'أرمل' ? 'selected' : ''; ?>>أرمل</option>
                        <option value="مطلق" <?php echo $filter_marital_status == 'مطلق' ? 'selected' : ''; ?>>مطلق</option>
                        <option value="يعول" <?php echo $filter_marital_status == 'يعول' ? 'selected' : ''; ?>>يعول</option>
                    </select>
                </div>
                
                <div class="col-md-2 mb-3">
                    <label for="governorate" class="form-label">المحافظة</label>
                    <select class="form-select" id="governorate" name="governorate">
                        <option value="">الكل</option>
                        <?php foreach (getEgyptianGovernorates() as $gov): ?>
                            <option value="<?php echo $gov; ?>" <?php echo $filter_governorate == $gov ? 'selected' : ''; ?>>
                                <?php echo $gov; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-1 mb-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>

            <!-- فلاتر إضافية للتجنيد والخدمة العامة -->
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label for="military_status" class="form-label">حالة التجنيد (للذكور)</label>
                    <select class="form-select" id="military_status" name="military_status">
                        <option value="">الكل</option>
                        <option value="قام بالتجنيد" <?php echo $filter_military_status == 'قام بالتجنيد' ? 'selected' : ''; ?>>قام بالتجنيد</option>
                        <option value="إعفاء مؤقت" <?php echo $filter_military_status == 'إعفاء مؤقت' ? 'selected' : ''; ?>>إعفاء مؤقت</option>
                        <option value="إعفاء نهائي" <?php echo $filter_military_status == 'إعفاء نهائي' ? 'selected' : ''; ?>>إعفاء نهائي</option>
                        <option value="تأجيل التجنيد" <?php echo $filter_military_status == 'تأجيل التجنيد' ? 'selected' : ''; ?>>تأجيل التجنيد</option>
                        <option value="غير محدد" <?php echo $filter_military_status == 'غير محدد' ? 'selected' : ''; ?>>غير محدد</option>
                    </select>
                </div>

                <div class="col-md-3 mb-3">
                    <label for="public_service_status" class="form-label">الخدمة العامة (للإناث)</label>
                    <select class="form-select" id="public_service_status" name="public_service_status">
                        <option value="">الكل</option>
                        <option value="تأدية الخدمة" <?php echo $filter_public_service_status == 'تأدية الخدمة' ? 'selected' : ''; ?>>تأدية الخدمة</option>
                        <option value="لم تؤدى الخدمة" <?php echo $filter_public_service_status == 'لم تؤدى الخدمة' ? 'selected' : ''; ?>>لم تؤدى الخدمة</option>
                        <option value="غير محدد" <?php echo $filter_public_service_status == 'غير محدد' ? 'selected' : ''; ?>>غير محدد</option>
                    </select>
                </div>

                <div class="col-md-6 mb-3 d-flex align-items-end">
                    <div class="btn-group w-100">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>بحث متقدم
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="exportFilteredData()">
                            <i class="fas fa-download me-2"></i>تصدير النتائج
                        </button>
                    </div>
                </div>
            </div>

            <?php if (!empty($search) || !empty($filter_gender) || !empty($filter_qualification) || !empty($filter_marital_status) || !empty($filter_governorate) || !empty($filter_military_status) || !empty($filter_public_service_status)): ?>
            <div class="row">
                <div class="col-12">
                    <a href="view_employees.php" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-times me-2"></i>مسح الفلاتر
                    </a>
                </div>
            </div>
            <?php endif; ?>
        </form>
    </div>
</div>

<!-- جدول الموظفين -->
<div class="card">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="fas fa-table me-2"></i>
            قائمة الموظفين
            <?php if ($total_records > 0): ?>
                <small class="text-muted">
                    (عرض <?php echo $offset + 1; ?> - <?php echo min($offset + $per_page, $total_records); ?> من <?php echo $total_records; ?>)
                </small>
            <?php endif; ?>
        </h6>
    </div>
    <div class="card-body">
        <?php if (empty($employees)): ?>
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد بيانات موظفين</h5>
                <p class="text-muted">لم يتم العثور على موظفين بالمعايير المحددة</p>
                <?php if (hasPermission('hr_employee')): ?>
                    <a href="add_employee.php" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i>إضافة موظف جديد
                    </a>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover" id="employeesTable">
                    <thead>
                        <tr>
                            <th>كود الموظف</th>
                            <th>الاسم الكامل</th>
                            <th>النوع</th>
                            <th>المؤهل</th>
                            <th>التخصص</th>
                            <th>المحافظة</th>
                            <th>التجنيد/الخدمة</th>
                            <th>الهاتف</th>
                            <th>تاريخ الإضافة</th>
                            <th>العمليات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($employees as $employee): ?>
                        <tr>
                            <td>
                                <span class="badge bg-primary"><?php echo $employee['employee_code']; ?></span>
                            </td>
                            <td>
                                <div>
                                    <strong><?php echo $employee['full_name']; ?></strong>
                                    <?php if ($employee['nickname']): ?>
                                        <br><small class="text-muted">(<?php echo $employee['nickname']; ?>)</small>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <span class="badge <?php echo $employee['gender'] == 'ذكر' ? 'bg-info' : 'bg-warning'; ?>">
                                    <?php echo $employee['gender']; ?>
                                </span>
                            </td>
                            <td><?php echo $employee['qualification']; ?></td>
                            <td><?php echo $employee['specialization'] ?: '-'; ?></td>
                            <td><?php echo $employee['governorate']; ?></td>
                            <td>
                                <?php if ($employee['gender'] == 'ذكر'): ?>
                                    <?php if ($employee['military_status'] != 'غير محدد'): ?>
                                        <?php
                                        $status_class = '';
                                        switch($employee['military_status']) {
                                            case 'قام بالتجنيد': $status_class = 'bg-success'; break;
                                            case 'إعفاء نهائي': $status_class = 'bg-warning'; break;
                                            case 'إعفاء مؤقت': $status_class = 'bg-info'; break;
                                            case 'تأجيل التجنيد': $status_class = 'bg-secondary'; break;
                                            default: $status_class = 'bg-light text-dark';
                                        }
                                        ?>
                                        <span class="badge <?php echo $status_class; ?> small">
                                            <?php echo $employee['military_status']; ?>
                                        </span>
                                    <?php else: ?>
                                        <small class="text-muted">غير محدد</small>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <?php if ($employee['public_service_status'] != 'غير محدد'): ?>
                                        <?php
                                        $service_class = '';
                                        switch($employee['public_service_status']) {
                                            case 'تأدية الخدمة': $service_class = 'bg-success'; break;
                                            case 'لم تؤدى الخدمة': $service_class = 'bg-danger'; break;
                                            default: $service_class = 'bg-light text-dark';
                                        }
                                        ?>
                                        <span class="badge <?php echo $service_class; ?> small">
                                            <?php echo $employee['public_service_status']; ?>
                                        </span>
                                    <?php else: ?>
                                        <small class="text-muted">غير محدد</small>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </td>
                            <td>
                                <a href="tel:<?php echo $employee['phone']; ?>" class="text-decoration-none">
                                    <?php echo $employee['phone']; ?>
                                </a>
                            </td>
                            <td>
                                <small class="text-muted">
                                    <?php echo formatDateTime($employee['created_at'], 'Y-m-d'); ?>
                                </small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="view_employee.php?id=<?php echo $employee['id']; ?>" 
                                       class="btn btn-outline-primary" 
                                       data-bs-toggle="tooltip" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <?php if (hasPermission('hr_employee')): ?>
                                    <a href="edit_employee.php?id=<?php echo $employee['id']; ?>" 
                                       class="btn btn-outline-warning" 
                                       data-bs-toggle="tooltip" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <?php endif; ?>
                                    <?php if (hasPermission('hr_manager')): ?>
                                    <button type="button" 
                                            class="btn btn-outline-danger" 
                                            data-bs-toggle="tooltip" title="حذف"
                                            onclick="deleteEmployee(<?php echo $employee['id']; ?>, '<?php echo $employee['full_name']; ?>')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- الترقيم -->
            <?php if ($total_pages > 1): ?>
            <nav aria-label="ترقيم الصفحات">
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                السابق
                            </a>
                        </li>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                    <?php endfor; ?>
                    
                    <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                التالي
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<script>
// حذف موظف
function deleteEmployee(id, name) {
    if (confirm('هل أنت متأكد من حذف الموظف: ' + name + '؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
        fetch('delete_employee.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({id: id})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'حدث خطأ أثناء الحذف');
            }
        })
        .catch(error => {
            alert('خطأ في الاتصال');
            console.error('Error:', error);
        });
    }
}

// تصدير إلى Excel (يتطلب مكتبة XLSX)
function exportToExcel(tableId, filename) {
    // يمكن إضافة مكتبة XLSX هنا
    alert('سيتم إضافة وظيفة التصدير قريباً');
}

// تصدير البيانات المفلترة
function exportFilteredData() {
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.set('export', 'excel');

    // إنشاء رابط تحميل مؤقت
    const link = document.createElement('a');
    link.href = currentUrl.toString();
    link.download = 'employees_filtered.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// تحسين فلاتر البحث حسب النوع
document.addEventListener('DOMContentLoaded', function() {
    const genderFilter = document.getElementById('gender');
    const militaryFilter = document.getElementById('military_status');
    const publicServiceFilter = document.getElementById('public_service_status');

    function toggleServiceFilters() {
        const selectedGender = genderFilter.value;

        if (selectedGender === 'ذكر') {
            militaryFilter.disabled = false;
            publicServiceFilter.disabled = true;
            publicServiceFilter.value = '';
        } else if (selectedGender === 'أنثى') {
            militaryFilter.disabled = true;
            militaryFilter.value = '';
            publicServiceFilter.disabled = false;
        } else {
            militaryFilter.disabled = false;
            publicServiceFilter.disabled = false;
        }
    }

    // تطبيق الفلاتر عند تحميل الصفحة
    toggleServiceFilters();

    // إضافة مستمع للتغيير
    genderFilter.addEventListener('change', toggleServiceFilters);
});
</script>

<?php include '../includes/footer.php'; ?>
